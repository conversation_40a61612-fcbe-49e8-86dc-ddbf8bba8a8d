<template>
  <view class="container">
    <view class="canvas-container">
      <canvas 
        v-for="(item, index) in canvasList" 
        :key="index"
        @touchmove="handleTouch(index, $event)"
        :canvas-id="'bubbleCanvas' + index"
        :style="{ 
          width: item.width + '%', 
          height: '100%',
          maxWidth: '100%', // 限制最大宽度为100%		  
          border: '1px solid #fff' // 添加边框
        }"
        willReadFrequently="true"
      ></canvas>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      canvasList: [
        { width: 10 },  // 各画布宽度比例
        { width: 18 },
        { width: 24 },
        { width: 24 },
        { width: 24 }
      ],
      bubbleSystems: []
    };
  },
  onReady() {
    this.initAllCanvas();
  },
  onUnload() {
    this.bubbleSystems.forEach(sys => {
      cancelAnimationFrame(sys.animationFrameId);
    });
  },
  methods: {
    initAllCanvas() {
      const systemInfo = uni.getSystemInfoSync();
      const totalRatio = this.canvasList.reduce((sum, c) => sum + c.width, 0);

      // 计算每个 Canvas 的像素宽度
      this.canvasList.forEach(canvas => {
        canvas.pxWidth = (systemInfo.windowWidth * canvas.width) / totalRatio;
      });

      this.bubbleSystems = this.canvasList.map((canvas, index) => {
        const ctx = uni.createCanvasContext(`bubbleCanvas${index}`, this);
        return {
          ctx,
          width: canvas.pxWidth,  // 直接使用预先计算的像素宽度
          height: systemInfo.windowHeight,
          bubbles: [],
          animationFrameId: null
        };
      });

      const bubbleCounts = [26, 46, 61, 58, 60]; // 每个区域生成泡泡数量
      this.bubbleSystems.forEach((sys, index) => {
        this.createBubbles(sys, bubbleCounts[index]); // 根据指定数量生成泡泡
        this.animate(sys);
      });
    },

    createBubbles(system, count) {
      const { width, height } = system;
      for (let i = 0; i < count; i++) {
        let newBubble;
        let overlapping;
        do {
          overlapping = false;
          newBubble = {
            x: Math.random() * (width - 10) + 5, // 确保泡泡在区域内
            y: Math.random() * (height - 10) + 5, // 确保泡泡在区域内
            radius: Math.random() * 15 + 5,
            dx: (Math.random() - 0.5) * 2,
            dy: (Math.random() - 0.5) * 2,
            color: this.generateBubbleColor(),
            growthRate: 0.1,
            systemWidth: width,
            systemHeight: height
          };

          system.bubbles.forEach(existing => {
            const dx = newBubble.x - existing.x;
            const dy = newBubble.y - existing.y;
            if (Math.sqrt(dx*dx + dy*dy) < newBubble.radius + existing.radius) {
              overlapping = true;
            }
          });
        } while (overlapping);
        system.bubbles.push(newBubble);
      }
    },

    generateBubbleColor() {
      const hue = Math.random() * 360;
      return {
        base: `rgba(${Math.floor(hue * 255 / 360)}, 180, 220, 0.2)`,
        highlight: `rgba(255, 255, 255, 0.8)`,
        edge: `rgba(${Math.floor(hue * 255 / 360)}, 160, 200, 0.1)`
      };
    },

    handleTouch(index, e) {
      const system = this.bubbleSystems[index];
      const { x, y } = e.touches[0];
      
      system.bubbles.forEach(bubble => {
        const dx = x - bubble.x;
        const dy = y - bubble.y;
        const distance = Math.sqrt(dx*dx + dy*dy);
        if (distance < 50) {
          bubble.dx += dx * 0.01;
          bubble.dy += dy * 0.01;
        }
      });
    },

    updateBubbles(system) {
      system.bubbles.forEach(bubble => {
        // 在各自区域内运动
        bubble.x += bubble.dx;
        bubble.y += bubble.dy;

        // 边界检测（使用各自canvas的尺寸）
        if (bubble.x < bubble.radius) {
          bubble.x = bubble.radius; // 确保泡泡不超出左边界
          bubble.dx *= -0.95; // 反弹
        }
        if (bubble.x > system.width - bubble.radius) {
          bubble.x = system.width - bubble.radius; // 确保泡泡不超出右边界
          bubble.dx *= -0.95; // 反弹
        }
        if (bubble.y < bubble.radius) {
          bubble.y = bubble.radius; // 确保泡泡不超出上边界
          bubble.dy *= -0.95; // 反弹
        }
        if (bubble.y > system.height - bubble.radius) {
          bubble.y = system.height - bubble.radius; // 确保泡泡不超出下边界
          bubble.dy *= -0.95; // 反弹
        }

        // 大小变化
        const oldRadius = bubble.radius;
        bubble.radius += bubble.growthRate;
        if (bubble.radius > 20 || bubble.radius < 2) {
          bubble.growthRate *= -1;
        }

        // 碰撞检测（仅检测同系统内的泡泡）
        system.bubbles.forEach(other => {
          if (bubble !== other) {
            const dx = bubble.x - other.x;
            const dy = bubble.y - other.y;
            const distance = Math.sqrt(dx*dx + dy*dy);
            if (distance < bubble.radius + other.radius) {
              // 调整速度和位置以防止重叠
              const overlap = bubble.radius + other.radius - distance;
              const angle = Math.atan2(dy, dx);
              const moveX = Math.cos(angle) * overlap / 2;
              const moveY = Math.sin(angle) * overlap / 2;

              bubble.x += moveX;
              bubble.y += moveY;
              other.x -= moveX;
              other.y -= moveY;

              // 交换速度
              [bubble.dx, other.dx] = [other.dx, bubble.dx];
              [bubble.dy, other.dy] = [other.dy, bubble.dy];
            }
          }
        });
      });
    },

    drawBubbles(system) {
      const { ctx, width, height } = system;
      ctx.clearRect(0, 0, width, height);

      system.bubbles.forEach(bubble => {
        // 绘制渐变
        const gradient = ctx.createLinearGradient(
          bubble.x - bubble.radius,
          bubble.y - bubble.radius,
          bubble.x + bubble.radius,
          bubble.y + bubble.radius
        );
        gradient.addColorStop(0, bubble.color.highlight);
        gradient.addColorStop(0.5, bubble.color.base);
        gradient.addColorStop(1, bubble.color.edge);

        // 绘制泡泡
        ctx.beginPath();
        ctx.arc(bubble.x, bubble.y, bubble.radius, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.shadowColor = 'rgba(255,255,255,0.3)';
        ctx.shadowBlur = 10;
        ctx.fill();

        // 绘制高光
        ctx.beginPath();
        ctx.arc(
          bubble.x - bubble.radius * 0.3,
          bubble.y - bubble.radius * 0.3,
          bubble.radius * 0.2,
          0, Math.PI * 2
        );
        ctx.fillStyle = 'rgba(255,255,255,0.6)';
        ctx.fill();
      });

      ctx.draw();
    },

    animate(system) {
      const animateFrame = () => {
        this.updateBubbles(system);
        this.drawBubbles(system);
        system.animationFrameId = requestAnimationFrame(animateFrame);
      };
      animateFrame();
    }
  }
};
</script>

<style>
.container {
  height: 100vh;
  overflow-x: auto;
}

.canvas-container {
  height: 100%;
  display: flex;
  flex-direction: row;
  background: #000;
}

canvas {
  height: 100%;
  flex-shrink: 0;
}
</style>