<template>
  <view class="container">
    <view class="canvas-container">
      <canvas 
        v-for="(item, index) in canvasList" 
        :key="index"
        :id="'bubbleCanvas' + index"
        :canvas-id="'bubbleCanvas' + index"
        @click="handleClick(index, $event)"
        :style="{ 
          width: item.width + '%', 
          height: '100%',
          border: '1px solid #fff'
        }"
        willReadFrequently="true">
      </canvas>
    </view>

    <!-- 信息弹窗 -->
    <view v-if="selectedBubble" class="popup-mask" @click="closePopup">
      <view class="popup" @click.stop>
        <image :src="selectedBubble.image" mode="aspectFit" class="popup-image"></image>
        <text class="popup-text">{{ selectedBubble.text }}</text>
        <button class="popup-btn" @click="closePopup">关闭</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      canvasList: [
        { width: 10 }, { width: 18 }, { width: 24 }, { width: 24 }, { width: 24 }
      ],
      bubbleSystems: [],
      selectedBubble: null,
      clickLock: false,
      imageList: [
        '/static/images/bubble1.jpg',
        '/static/images/bubble2.jpg',
        '/static/images/bubble3.jpg',
        '/static/images/bubble4.jpg',
        '/static/images/bubble5.jpg'
      ]
    };
  },
  onReady() {
    this.initAllCanvas();
  },
  onUnload() {
    this.bubbleSystems.forEach(sys => cancelAnimationFrame(sys.animationFrameId));
  },
  methods: {
    initAllCanvas() {
      const systemInfo = uni.getSystemInfoSync();
      const totalRatio = this.canvasList.reduce((sum, c) => sum + c.width, 0);

      this.bubbleSystems = this.canvasList.map((canvas, index) => {
        const pxWidth = (systemInfo.windowWidth * canvas.width) / totalRatio;
        return {
          ctx: uni.createCanvasContext(`bubbleCanvas${index}`, this),
          width: pxWidth,
          height: systemInfo.windowHeight,
          bubbles: [],
          animationFrameId: null
        };
      });

      const bubbleCounts = [26, 46, 61, 58, 60];
      this.bubbleSystems.forEach((sys, index) => {
        this.createBubbles(sys, bubbleCounts[index]);
        this.animate(sys);
      });
    },

    createBubbles(system, count) {
      const { width, height } = system;
      for (let i = 0; i < count; i++) {
        let newBubble;
        let overlapping;
        do {
          overlapping = false;
          newBubble = {
            x: Math.random() * (width - 20) + 10,
            y: Math.random() * (height - 20) + 10,
            radius: Math.random() * 15 + 5,
            dx: (Math.random() - 0.5) * 2,
            dy: (Math.random() - 0.5) * 2,
            color: this.generateBubbleColor(),
            image: this.imageList[i % this.imageList.length],
            text: `泡泡主题 ${i+1}\n详细信息...`,
            growthRate: 0.1
          };

          system.bubbles.forEach(existing => {
            const dx = newBubble.x - existing.x;
            const dy = newBubble.y - existing.y;
            if (Math.sqrt(dx*dx + dy*dy) < newBubble.radius + existing.radius) {
              overlapping = true;
            }
          });
        } while (overlapping);
        system.bubbles.push(newBubble);
      }
    },

    generateBubbleColor() {
      const hue = Math.floor(Math.random() * 360);
      return {
        base: `rgba(${hue}, 180, 220, 0.2)`,
        highlight: `rgba(255, 255, 255, 0.8)`,
        edge: `rgba(${hue}, 160, 200, 0.1)`
      };
    },

handleClick(index, e) {
  if (this.clickLock) return;
  this.clickLock = true;

  const system = this.bubbleSystems[index];
  const query = uni.createSelectorQuery().in(this);

  query.select(`#bubbleCanvas${index}`).boundingClientRect(rect => {
    if (!rect) {
      console.error('Canvas元素未找到');
      this.clickLock = false;
      return;
    }

    // 获取触摸点坐标
    const touch = e.touches ? e.touches[0] : e.mp.touches[0];
    const x = touch.clientX - rect.left;
    const y = touch.clientY - rect.top;

    // 计算缩放比例
    const scaleX = system.width / rect.width;
    const scaleY = system.height / rect.height;

    // 转换为Canvas内部坐标
    const canvasX = x * scaleX;
    const canvasY = y * scaleY;

    // 查找被点击的泡泡
    system.bubbles.some(bubble => {
      const dx = canvasX - bubble.x;
      const dy = canvasY - bubble.y;
      if (Math.sqrt(dx * dx + dy * dy) < bubble.radius) {
        this.selectedBubble = {
          ...bubble,
          systemIndex: index
        };
        return true; // 找到后提前终止遍历
      }
    });

    this.clickLock = false;
  }).exec();
},

    updateBubbles(system) {
      system.bubbles.forEach(bubble => {
        // 在各自区域内运动
        bubble.x += bubble.dx;
        bubble.y += bubble.dy;

        // 边界检测（使用各自canvas的尺寸）
        if (bubble.x < bubble.radius) {
          bubble.x = bubble.radius; // 确保泡泡不超出左边界
          bubble.dx *= -0.95; // 反弹
        }
        if (bubble.x > system.width - bubble.radius) {
          bubble.x = system.width - bubble.radius; // 确保泡泡不超出右边界
          bubble.dx *= -0.95; // 反弹
        }
        if (bubble.y < bubble.radius) {
          bubble.y = bubble.radius; // 确保泡泡不超出上边界
          bubble.dy *= -0.95; // 反弹
        }
        if (bubble.y > system.height - bubble.radius) {
          bubble.y = system.height - bubble.radius; // 确保泡泡不超出下边界
          bubble.dy *= -0.95; // 反弹
        }

        // 大小变化
        const oldRadius = bubble.radius;
        bubble.radius += bubble.growthRate;
        if (bubble.radius > 20 || bubble.radius < 2) {
          bubble.growthRate *= -1;
        }

        // 碰撞检测（仅检测同系统内的泡泡）
        system.bubbles.forEach(other => {
          if (bubble !== other) {
            const dx = bubble.x - other.x;
            const dy = bubble.y - other.y;
            const distance = Math.sqrt(dx*dx + dy*dy);
            if (distance < bubble.radius + other.radius) {
              // 调整速度和位置以防止重叠
              const overlap = bubble.radius + other.radius - distance;
              const angle = Math.atan2(dy, dx);
              const moveX = Math.cos(angle) * overlap / 2;
              const moveY = Math.sin(angle) * overlap / 2;

              bubble.x += moveX;
              bubble.y += moveY;
              other.x -= moveX;
              other.y -= moveY;

              // 交换速度
              [bubble.dx, other.dx] = [other.dx, bubble.dx];
              [bubble.dy, other.dy] = [other.dy, bubble.dy];
            }
          }
        });
      });
    },

    drawBubbles(system) {
      const { ctx, width, height } = system;
      ctx.clearRect(0, 0, width, height);

      system.bubbles.forEach(bubble => {
        // 绘制渐变
        const gradient = ctx.createLinearGradient(
          bubble.x - bubble.radius,
          bubble.y - bubble.radius,
          bubble.x + bubble.radius,
          bubble.y + bubble.radius
        );
        gradient.addColorStop(0, bubble.color.highlight);
        gradient.addColorStop(0.5, bubble.color.base);
        gradient.addColorStop(1, bubble.color.edge);

        // 绘制泡泡
        ctx.beginPath();
        ctx.arc(bubble.x, bubble.y, bubble.radius, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.shadowColor = 'rgba(255,255,255,0.3)';
        ctx.shadowBlur = 10;
        ctx.fill();

        // 绘制高光
        ctx.beginPath();
        ctx.arc(
          bubble.x - bubble.radius * 0.3,
          bubble.y - bubble.radius * 0.3,
          bubble.radius * 0.2,
          0, Math.PI * 2
        );
        ctx.fillStyle = 'rgba(255,255,255,0.6)';
        ctx.fill();
		
/*        // 绘制标题
        ctx.fillStyle = 'rgba(255,255,255,0.9)';
        //ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        let fontSize = bubble.radius * 0.5;
        ctx.setFontSize(fontSize);
        const textWidth = ctx.measureText(bubble.text).width;
        ctx.fillText(bubble.text, bubble.x - Math.min(bubble.radius - 5, textWidth / 2), bubble.y); // 在泡泡上方绘制标题		 */
		
      });

      ctx.draw();
    },

    animate(sys) {
      const animateFrame = () => {
        this.updateBubbles(sys);
        this.drawBubbles(sys);
        sys.animationFrameId = requestAnimationFrame(animateFrame);
      };
      animateFrame();
    },

    closePopup() {
      this.selectedBubble = null;
    }
  }
};
</script>

<style>
.container {
  height: 100vh;
  overflow-x: auto;
}

.canvas-container {
  height: 100%;
  display: flex;
  background: #000;
}

canvas {
  height: 100%;
  flex-shrink: 0;
}

.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.popup {
  background: rgba(0,0,0,0.9);
  padding: 20px;
  border-radius: 12px;
  border: 2px solid #fff;
  width: 80%;
  max-width: 400px;
}

.popup-image {
  width: 200px;
  height: 200px;
  margin-bottom: 15px;
}

.popup-text {
  color: #fff;
  font-size: 16px;
  text-align: center;
  margin-bottom: 20px;
  white-space: pre-line;
}

.popup-btn {
  background: #007AFF;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
}
</style>