-- 修改 Children 表中的 nchar 字段为 nvarchar 字段
-- 执行前请确保备份数据库

USE [edsynwx]
GO

-- 修改 gender 字段从 nchar(10) 到 nvarchar(10)
ALTER TABLE [dbo].[Children]
ALTER COLUMN [gender] [nvarchar](10) NULL;
GO

-- 修改 cardNumber 字段从 nchar(20) 到 nvarchar(20)
ALTER TABLE [dbo].[Children]
ALTER COLUMN [cardNumber] [nvarchar](20) NULL;
GO

-- 修改 cardType 字段从 nchar(15) 到 nvarchar(15)
ALTER TABLE [dbo].[Children]
ALTER COLUMN [cardType] [nvarchar](15) NULL;
GO

-- 验证修改结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Children' 
    AND COLUMN_NAME IN ('gender', 'cardNumber', 'cardType')
ORDER BY COLUMN_NAME;
GO
