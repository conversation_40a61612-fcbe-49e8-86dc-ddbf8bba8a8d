USE [edsynwx]
GO

/****** Object:  Table [dbo].[Children]    Script Date: 2025/7/1 13:49:43 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Children](
	[childId] [int] IDENTITY(1,1) NOT NULL,
	[parentId] [int] NOT NULL,
	[name] [nvarchar](50) NOT NULL,
	[school] [nvarchar](100) NULL,
	[grade] [nvarchar](20) NULL,
	[birthDate] [date] NULL,
	[createTime] [datetime] NULL,
	[updateTime] [datetime] NULL,
	[gender] [nvarchar](10) NULL,
	[cardNumber] [nvarchar](20) NULL,
	[cardType] [nvarchar](15) NULL,
	[StoreID] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[childId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE [dbo].[Children] ADD  DEFAULT (getdate()) FOR [createTime]
GO

ALTER TABLE [dbo].[Children] ADD  DEFAULT (getdate()) FOR [updateTime]
GO

ALTER TABLE [dbo].[Children]  WITH CHECK ADD FOREIGN KEY([parentId])
REFERENCES [dbo].[Parents] ([id])
ON DELETE CASCADE
GO


