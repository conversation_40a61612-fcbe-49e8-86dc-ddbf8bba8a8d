<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
<title>Bubbles</title>
<style>
html, body {
    border: none;
    overflow: hidden;
    height: 100%;
    margin: 0;
    padding: 0;
}

body { 
    background: url(BG.jpg) bottom; 
    position: relative;
    display: flex;
    flex-direction: row; /* 让区域水平排列 */
}

/* 5 个水平排列的区域 */
.region {
    height: 100%; /* 区域占满整个高度 */
    border-left: 2px solid rgba(33, 33, 33, 0.8);
    position: relative;
}
/* 利用伪元素在每个分区底部添加标注，类似CAD测量距离的风格 */
.region::after {
    content: attr(data-label);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.85);
    background: rgba(0, 0, 0, 0.5);
    padding: 4px 0;
    border-top: 1px dashed rgba(255, 255, 255, 0.7);
}

/* 区域背景色均采用半透明黑色 */
.region:nth-child(1),
.region:nth-child(2),
.region:nth-child(3),
.region:nth-child(4),
.region:nth-child(5) {
    background: rgba(0, 0, 0, 0.2);
}
.region:nth-child(1) { width: 10%; }
.region:nth-child(2) { width: 18%; }
.region:nth-child(3) { width: 24%; }
.region:nth-child(4) { width: 24%; }
.region:nth-child(5) { width: 24%; }

/* 美化弹出窗口 */
.popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #ffffff, #f7f7f7);
    border: 2px solid #333;
    border-radius: 8px;
    padding: 30px;
    z-index: 1000;
    width: 80%;
    max-width: 1000px;
    max-height: 80vh;      /* 最大高度80%视口高度 */
    overflow-y: auto;      /* 超出内容出现竖向滚动条 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-align: center;
}

.popup h2 {
    margin-top: 0;
    color: #333;
}

.popup p {
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    text-align: left;
    margin: 15px 0;
}

.popup img {
    max-width: 80%;
    height: auto;
    margin: 20px 0;
}
.audio-player {
    width: 80%;
    margin: 20px auto;
    text-align: center;
}
#bubbleId {
    display: none; /* 隐藏泡泡ID显示 */
}

#closeButton {
    display: block; /* 改为block使其独占一行 */
    width: 100%; /* 宽度100% */
    text-align: center; /* 文字居中 */
    background-color: #333;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    margin-top: 20px; /* 增加上边距 */
    margin-bottom: 10px; /* 增加下边距 */
    box-sizing: border-box; /* 包含padding在宽度内 */
}

#closeButton:hover {
    background-color: #555;
}

/* 返回首页按钮样式，固定在页面右下角 */
.back-btn {
    position: fixed;
    right: 20px;
    bottom: 35px;
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    padding: 10px 20px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    text-decoration: none;
    transition: background 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}
.back-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
}
</style>
</head>

<body onload="Demo()">
    <!-- 5 个水平排列的区域，设置 data-label 属性标注分区名称 -->
    <div class="region" data-label="公元前2.6万年-公元0年"></div>
    <div class="region" data-label="50-1798年"></div>
    <div class="region" data-label="1800-1899年"></div>
    <div class="region" data-label="1900-1949年"></div>
    <div class="region" data-label="1949-2024年"></div>

    <!-- 弹出窗口，内容为图文信息，文字可能较长 -->
    <div class="popup" id="bubblePopup">
        <h2></h2>
        <p id="bubbleId"></p>
        <!-- 这里可以显示1000字左右的长文本 -->
        <p id="bubbleText">
        </p>
        <img id="bubbleImage" src="">
        <div class="audio-player">
            <audio id="bubbleAudio" controls autoplay>
                <source src="" type="audio/mpeg">
                您的浏览器不支持音频播放。
            </audio>
        </div>        
        <button id="closeButton">关闭</button>
    </div>
    <!-- 返回首页按钮，固定在右下角 -->
    <a href="index.html" class="back-btn">HOME</a>

<script>
// 定义每个区域目标泡泡数及当前计数
var bubbleTarget = [26, 46, 61, 58, 60]; 
var bubbleCount = [0, 0, 0, 0, 0];
var bubbleIdCounter = 1; // 唯一ID计数器
var currentRegionIndex = 0; // 当前生成泡泡的区域索引

// 添加泡泡标题数组
var bubbleTitles = [
"伊尚戈骨骸",
"小麦：生命的主粮",
"农业",
"动物驯养",
"水稻",
"宇宙学的诞生",
"青铜",
"骰子",
"日晷",
"伤口缝合",
"古埃及天文学",
"拱形",
"莱因德纸草书",
"铁的冶炼",
"奥尔梅克罗盘",
"毕达哥拉斯定理与毕氏三角形",
"污水处理系统",
"亚里士多德的《工具论》",
"柏拉图多面体",
"欧几里得和《几何原本》",
"阿基米德的浮力原理",
"圆周率π",
"埃拉托色尼测量地球",
"埃拉托色尼的筛法",
"滑轮",
"安提基特拉机械",
"齿轮",
"罗马混凝土",
"零的出现",
"花拉子密的《代数》",
"火药",
"斐波那契的《计算书》",
"眼镜",
"早期微积分",
"黄金比例",
"人体的构造",
"日心宇宙学说",
"巴累的“合理手术”",
"虚数",
"望远镜",
"开普勒行星运动定律",
"对数",
"科学方法",
"计算尺",
"血液循环系统",
"笛卡尔的《几何学》",
"重力加速度",
"射影几何",
"帕斯卡三角形",
"冯·居里克静电起电机",
"发明微积分",
"《显微图谱》",
"驳斥自然发生说",
"测量太阳系",
"牛顿棱镜",
"精子的发现",
"人体内的“动物园”",
"牛顿——伟大的启迪者",
"牛顿运动定律和万有引力定律",
"大数定律",
"欧拉数（e）",
"正态分布曲线",
"林奈的生物分类法",
"伯努利流体动力学定律",
"人工选择（选择育种）",
"贝叶斯定理",
"癌症病因",
"莫尔加尼：“病变器官的哭喊”",
"黑洞",
"库仑定律",
"代数基本定理",
"牛痘接种",
"电池",
"高压蒸汽机",
"光的波动性",
"傅里叶级数",
"原子论",
"拉普拉斯的《概率的分析理论》",
"巴贝奇的机械计算机",
"卡诺热机",
"温室效应",
"安培电磁定律",
"布朗运动",
"发育的胚层学说",
"输血",
"非欧几里得几何",
"细胞核",
"达尔文与贝格尔号之旅",
"法拉第电磁感应定律",
"化石记录和进化",
"氮循环和植物化学",
"电报系统",
"银版照相法",
"橡胶",
"光纤光学",
"全身麻醉",
"能量守恒",
"超越数",
"塞麦尔维斯：教会医生洗手的人",
"热力学第二定律",
"贝塞麦炼钢法",
"细胞分裂",
"塑料",
"莫比乌斯带",
"达尔文的自然选择理论",
"生态相互作用",
"分子运动论",
"黎曼假设",
"大脑功能定位",
"麦克斯韦方程组",
"病原菌学说",
"电磁波谱",
"消毒剂",
"孟德尔遗传学",
"元素周期表",
"康托尔的超限数",
"玻尔兹曼熵方程",
"吉布斯自由能",
"电话",
"酶",
"白炽灯",
"电网",
"迈克尔逊-莫雷实验",
"超立方体",
"蒸汽轮机",
"心理学原理",
"神经元学说",
"病毒的发现",
"X射线",
"质数定理的证明",
"放射性",
"电子",
"精神分析",
"黑体辐射定律",
"希尔伯特的23个问题",
"遗传的染色体理论",
"莱特兄弟的飞机",
"经典条件反射",
"E=mc2",
"光电效应",
"狭义相对论",
"内燃机",
"水的氯化",
"主序星",
"原子核",
"超导电性",
"布拉格晶体衍射定律",
"大陆漂移说",
"玻尔原子模型",
"广义相对论",
"弦论",
"氢键",
"广播电台",
"诺特的理想环理论",
"爱因斯坦——伟大的启迪者",
"德布罗意公式",
"泡利不相容原理",
"薛定谔人波动方程",
"互补性原理",
"食物网",
"海森堡不确定性原理",
"昆虫的舞蹈语言",
"狄拉克方程",
"青霉素",
"哈勃宇宙膨胀定律",
"哥德尔定理",
"反物质",
"中子",
"暗物质",
"聚乙烯",
"中子星",
"EPR佯谬",
"薛定谔的猫",
"图灵机",
"细胞呼吸",
"超流体",
"核磁共振",
"掺杂硅",
"来自原子核的能量",
"“小男孩”原子弹",
"铀浓缩",
"ENIAC",
"恒星核合成",
"全息图",
"光合作用",
"晶体管",
"信息论",
"量子电动力学",
"随机对照试验",
"放射性碳测年法",
"时间旅行",
"弈棋机",
"费米悖论",
"海拉细胞",
"元胞自动机",
"米勒-尤列实验",
"DNA的结构",
"原子钟",
"避孕药",
"安慰剂",
"核糖体",
"平行宇宙",
"抗抑郁药物",
"人造卫星",
"分子生物学的中心法则",
"集成电路",
"抗体的结构",
"激光",
"破解蛋白质生物合成的遗传密码",
"第一批宇航员",
"绿色革命",
"标准模型",
"混沌和蝴蝶效应",
"认知行为疗法",
"大脑偏侧性",
"夸克",
"宇宙微波背景辐射",
"动态随机存取存储器",
"内共生学说",
"心脏移植",
"土星5号火箭",
"阿帕网",
"第一次登月",
"基因工程",
"费根鲍姆常数",
"分形",
"公钥密码学",
"心智理论",
"引力透镜",
"宇宙暴胀",
"量子计算机",
"人工心脏",
"表观遗传学",
"聚合酶连式反应",
"端粒酶",
"万物理论",
"线粒体夏娃",
"生物域",
"哈勃空间望远镜",
"万维网",
"全球定位系统",
"暗能量",
"国际空间站",
"人类基因组计划",
"勇气号和机遇号在火星",
"克隆人类",
"大型强子对撞机",
"基因治疗",
"引力波",
"证明开普勒猜想",
"智能机器人和人工智能",
];

function Demo() {
    if (currentRegionIndex < bubbleTarget.length) {
        if (bubbleCount[currentRegionIndex] < bubbleTarget[currentRegionIndex]) {
            CreateBubble(currentRegionIndex);
            bubbleCount[currentRegionIndex]++;
        }
        if (bubbleCount[currentRegionIndex] < bubbleTarget[currentRegionIndex]) {
            setTimeout(Demo, 0);
        } else {
            currentRegionIndex++;
            setTimeout(Demo, 0);
        }
    }
}

+function() {
    var _VER_ = navigator.userAgent;
    var _IE6_ = /IE 6/.test(_VER_);
    var STD = !!window.addEventListener;
    var de = document.documentElement;

    _IE6_ && document.execCommand("BackgroundImageCache", false, true);

    var K = 0.999;
    var POW_RATE = 0.0001;
    var POW_RANGE = 0.8;

    function SPEED_X() { return 8 + RND() * 4; }
    function SPEED_Y() { return 6 + RND() * 2; }

    var arrBubs = [];
    var iBottom;
    var iRight;

    var SQRT = Math.sqrt;
    var ATAN2 = Math.atan2;
    var SIN = Math.sin;
    var COS = Math.cos;
    var ABS = Math.abs;
    var RND = Math.random;
    var ROUND = Math.round;

    function Timer(call, time) {
        var last = +new Date;
        var delay = 0;
        return setInterval(function() {
            var cur = +new Date;
            delay += (cur - last);
            last = cur;
            if (delay >= time) {
                call();
                delay %= time;
            }
        }, 1);
    }

    Timer(update, 17);

    function CreateBubble(regionIndex) {
        var regions = document.querySelectorAll(".region");
        var region = regions[regionIndex];
        var regionRect = region.getBoundingClientRect();

        var bub = new Bubble(10, bubbleIdCounter++);
        var startX = regionRect.left + Math.random() * (regionRect.width - 10);
        var startY = regionRect.top + Math.random() * (regionRect.height - 10);

        bub.setX(startX);
        bub.setY(startY);
        bub.vx = SPEED_X() * (Math.random() > 0.5 ? 1 : -1);
        bub.vy = SPEED_Y() * (Math.random() > 0.5 ? 1 : -1);
        bub.regionBounds = regionRect;

        arrBubs.push(bub);
        bub.oBox.onclick = function() { showPopup(bub.id); };
    }

    window.CreateBubble = CreateBubble;
    
    function update() {
        var n = arrBubs.length;
        var bub, bub2;

        updateWall();

        for (var i = 0; i < n; i++) {
            bub = arrBubs[i];
            bub.paint();
            bub.vx *= K;
            bub.vy *= K;

            if (RND() < 0.01) {
                var newTargetD = 10 + RND() * 80;
                bub.setTargetSize(newTargetD);
            }

            if (bub.D !== bub.targetD) {
                var delta = bub.targetD - bub.D;
                if (Math.abs(delta) < bub.sizeSpeed) {
                    bub.D = bub.targetD;
                } else {
                    bub.D += delta > 0 ? bub.sizeSpeed : -bub.sizeSpeed;
                }
                bub.updateSize();
            }

            if (RND() < POW_RATE) {
                bub.vx = SPEED_X() * (1 + RND() * POW_RANGE);
                bub.vy = SPEED_Y() * (1 + RND() * POW_RANGE);
            }

            bub.setX(bub.x + bub.vx);
            bub.setY(bub.y + bub.vy);
            checkWalls(bub);
        }

        for (var i = 0; i < n - 1; i++) {
            bub = arrBubs[i];
            for (var j = i + 1; j < n; j++) {
                bub2 = arrBubs[j];
                checkCollision(bub, bub2);
            }
        }
    }

    function updateWall() {
        iRight = de.clientWidth;
        iBottom = de.clientHeight;
    }

    function checkWalls(bub) {
        var bounds = bub.regionBounds;

        if (bub.x < bounds.left) {
            bub.setX(bounds.left);
            bub.vx *= -1;
        } else if (bub.x > bounds.right - bub.D) {
            bub.setX(bounds.right - bub.D);
            bub.vx *= -1;
        }

        if (bub.y < bounds.top) {
            bub.setY(bounds.top);
            bub.vy *= -1;
        } else if (bub.y > bounds.bottom - bub.D) {
            bub.setY(bounds.bottom - bub.D);
            bub.vy *= -1;
        }
    }

    function rotate(x, y, sin, cos, reverse) {
        if (reverse)
            return { x: x * cos + y * sin, y: y * cos - x * sin };
        else
            return { x: x * cos - y * sin, y: y * cos + x * sin };
    }

    function checkCollision(bub0, bub1) {
        var dx = bub1.x - bub0.x;
        var dy = bub1.y - bub0.y;
        var dist = SQRT(dx * dx + dy * dy);
        var minDist = (bub0.D + bub1.D) / 2;

        if (dist < minDist) {
            var angle = ATAN2(dy, dx);
            var sin = SIN(angle);
            var cos = COS(angle);

            var pos0 = { x: 0, y: 0 };
            var pos1 = rotate(dx, dy, sin, cos, true);

            var vel0 = rotate(bub0.vx, bub0.vy, sin, cos, true);
            var vel1 = rotate(bub1.vx, bub1.vy, sin, cos, true);

            var vxTotal = vel0.x - vel1.x;
            vel0.x = vel1.x;
            vel1.x = vxTotal + vel0.x;

            var absV = ABS(vel0.x) + ABS(vel1.x);
            var overlap = minDist - ABS(pos0.x - pos1.x);

            pos0.x += vel0.x / absV * overlap;
            pos1.x += vel1.x / absV * overlap;

            var pos0F = rotate(pos0.x, pos0.y, sin, cos, false);
            var pos1F = rotate(pos1.x, pos1.y, sin, cos, false);

            bub1.setX(bub0.x + pos1F.x);
            bub1.setY(bub0.y + pos1F.y);
            bub0.setX(bub0.x + pos0F.x);
            bub0.setY(bub0.y + pos0F.y);

            var vel0F = rotate(vel0.x, vel0.y, sin, cos, false);
            var vel1F = rotate(vel1.x, vel1.y, sin, cos, false);

            bub0.vx = vel0F.x;
            bub0.vy = vel0F.y;
            bub1.vx = vel1F.x;
            bub1.vy = vel1F.y;
        }
    }

    var APLHA = 0.8;
    var POW = [1, APLHA, APLHA * APLHA];

    function Bubble(initialD, id) {
        this.D = initialD;
        this.targetD = initialD;
        this.sizeSpeed = 2;
        this.id = id;
        var kOpa = [], kStp = [];
        var arrFlt = [];
        var oBox = document.body.appendChild(document.createElement("div"));

        styBox = oBox.style;
        styBox.position = "absolute";
        styBox.width = this.D + "px";
        styBox.height = this.D + "px";

        for (var i = 0; i < 4; i++) {
            var div = document.createElement("div");
            var sty = div.style;
            sty.position = "absolute";
            sty.width = this.D + "px";
            sty.height = this.D + "px";
            oBox.appendChild(div);
            if (i == 3) {
                if (_IE6_)
                    sty.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(src=heart.png)";
                else
                    sty.backgroundImage = "url(heart.png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                break;
            }
            kOpa[i] = 3 * RND();
            kStp[i] = 0.02 * RND();
            if (STD) {
                sty.backgroundImage = "url(ch" + i + ".png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                arrFlt[i] = sty;
            } else {
                sty.filter = "alpha progid:DXImageTransform.Microsoft.AlphaImageLoader(src=ch" + i + ".png)";
                arrFlt[i] = div.filters.alpha;
            }
        }

        this.styBox = styBox;
        this.oBox = oBox;
        this.kOpa = kOpa;
        this.kStp = kStp;
        this.arrFlt = arrFlt;
    }

    Bubble.prototype.setX = function(x) {
        this.x = x;
        this.styBox.left = ROUND(x) + "px";
    };

    Bubble.prototype.setY = function(y) {
        this.y = y;
        this.styBox.top = ROUND(y) + "px";
    };

    Bubble.prototype.setTargetSize = function(newTargetD) {
        this.targetD = newTargetD;
    };

    Bubble.prototype.updateSize = function() {
        this.styBox.width = this.D + "px";
        this.styBox.height = this.D + "px";
        for (var i = 0; i < this.oBox.children.length; i++) {
            var div = this.oBox.children[i];
            div.style.width = this.D + "px";
            div.style.height = this.D + "px";
            div.style.backgroundSize = this.D + "px " + this.D + "px";
        }
    };

    Bubble.prototype.paint = function() {
        var i, v;
        for (i = 0; i < 3; i++) {
            v = ABS(SIN(this.kOpa[i] += this.kStp[i] * RND()));
            v *= POW[i];
            v = ((v * 1e4) >> 0) / 1e4;
            this.arrFlt[i].opacity = STD ? v : v * 100;
        }
    };

    function showPopup(bubbleId) {
        var title = bubbleTitles[bubbleId - 1];
        document.getElementById('bubblePopup').querySelector('h2').innerText = title;
        document.getElementById('bubbleId').innerText = "泡泡 ID: " + bubbleId;
        document.getElementById('bubbleImage').src = "static/science/" + bubbleId + ".jpg";
        // 设置音频源
        document.getElementById('bubbleAudio').src = "static/science/" + bubbleId + ".mp3";
        // 使用fetch读取对应ID的txt文件
        fetch('static/science/' + bubbleId + '.txt')
            .then(response => response.text())
            .then(data => {
                document.getElementById('bubbleText').innerHTML = data;
            })
            .catch(error => {
                console.error('Error loading content:', error);
                document.getElementById('bubbleText').innerHTML = "内容加载失败";
            });
        document.getElementById('bubblePopup').style.display = 'block';
    }

    function closePopup() {
        document.getElementById('bubblePopup').style.display = 'none';
        // 获取音频元素并停止播放
        var audio = document.getElementById('bubbleAudio');
        audio.pause();
        audio.currentTime = 0; // 将播放进度重置到开头
    }

    document.getElementById('closeButton').onclick = closePopup;

}();
</script>
</body>
</html>
