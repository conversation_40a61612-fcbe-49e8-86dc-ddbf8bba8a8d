USE [edsynwx]
GO

/****** Object:  Table [dbo].[MemberCards]    Script Date: 2025/6/17 11:11:13 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[MemberCards](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[CardNumber] [varchar](20) NOT NULL,
	[CardType] [nvarchar](20) NOT NULL,
	[StartDate] [date] NOT NULL,
	[EndDate] [date] NOT NULL,
	[Status] [tinyint] NOT NULL,
	[CreateTime] [datetime] NULL,
	[UpdateTime] [datetime] NULL,
	[StoreID] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
UNIQUE NONCLUSTERED 
(
	[CardNumber] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[MemberCards] ADD  DEFAULT ('普通卡') FOR [CardType]
GO

ALTER TABLE [dbo].[MemberCards] ADD  DEFAULT ((1)) FOR [Status]
GO

ALTER TABLE [dbo].[MemberCards] ADD  DEFAULT (getdate()) FOR [CreateTime]
GO

ALTER TABLE [dbo].[MemberCards] ADD  DEFAULT (getdate()) FOR [UpdateTime]
GO


