<template>
  <view>
    <!-- 科学内容展示区 -->
    <view class="science-section">
      <view class="science-title">
        <text class="title">{{ science.title }}</text>
      </view>
      <view class="science-body">
        <view class="science-images">
          <!-- <text class="science-time">发布时间：{{ formatTime(science.createdAt) }}</text> -->
          <image :src="science.image" class="science-image"></image>
        </view>
        <view class="science-audio" v-if="science.audio">
          <button @click="playAudio">播放音频</button>
          <audio ref="audioPlayer" :src="science.audio"></audio>
        </view>
		<view class="science-content" v-html="science.content"></view>
      </view>
    </view>
  </view>
</template>

<script>
import { generateSignature } from '@/utils/auth'

export default {
  data() {
    return {
      science: {
        title: "",
        content: "",
        image: "",
        audio: "",
        createdAt: ""
      },
      scienceId: 1,
      innerAudioContext: null
    };
  },
  onLoad(options) {
    this.scienceId = options.scienceId || 1
    this.innerAudioContext = wx.createInnerAudioContext();
    this.fetchScience()
  },
  onUnload() {
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
    }
  },
  methods: {
    formatTime(timestamp) {
      if (!timestamp) return '未知时间'
      const date = new Date(timestamp)
      return `${date.getFullYear()}-${date.getMonth()+1}-${date.getDate()}`
    },
    playAudio() {
      if (this.innerAudioContext && this.science.audio) {
        this.innerAudioContext.src = this.science.audio;
        this.innerAudioContext.play();
      }
    },
    async fetchScience() {
      try {
        const timestamp = Math.floor(Date.now() / 1000)
        const nonce = Math.random().toString(36).substring(2, 10)
        const signature = generateSignature(timestamp, nonce)
        
        const res = await uni.request({
          url: 'https://aikexiaozhan.com/api/ScienceDetails',
          method: 'POST',
          data: { scienceId: this.scienceId },
          header: {
            'X-Timestamp': timestamp,
            'X-Nonce': nonce,
            'X-Signature': signature,
            'Content-Type': 'application/json'
          }
        })
        
        if (res.statusCode === 200) {
          this.science = res.data.data
        }
      } catch (error) {
        console.error('获取科学内容失败:', error)
      }
    }
  }
};
</script>

<style scoped>
.science-section {
  padding: 20px;
}

.science-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.science-content {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.science-images {
  display: block;
  flex-wrap: wrap;
  gap: 10px;
}

.science-image {
  width: 100%;
  max-width: 750rpx;
  height: 400rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.science-time {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
}

.science-audio {
  margin-top: 20px;
}

.science-content >>> p {
  margin-bottom: 10px;
}

.science-content >>> a {
  color: #007AFF;
  text-decoration: none;
}

.science-content >>> img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 10px 0;
}
</style>