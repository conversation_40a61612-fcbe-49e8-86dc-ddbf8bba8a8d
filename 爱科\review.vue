<template>
	<view>
	  <view class="header-title">
		爱迪生科学小站
	  </view>    	  
	  <swiper
		class="swiper-container"
		:autoplay="true"
		:circular="true"
		:indicator-dots="true"
		indicator-active-color="#007AFF"
		indicator-color="#CCC"
		:interval="3000"
		:duration="500">
		<swiper-item v-for="(item, index) in swiperlist1" :key="index">
		  <view class="swiper-item" @click="goToPage('swiperlist1', index)">
			<image :src="item.image" mode="aspectFill" class="swiper-image" />
			<view class="swiper-title">{{item.title}}</view>
		  </view>
		</swiper-item>
	  </swiper>
	  <!-- 视频分类展示区 -->
	  <view class="video-category">
		  <scroll-view scroll-x class="category-scroll">
			<view class="category-header">
			  <button v-for="(category, index) in categories" :key="index" 
					  :class="['category-button', { 'active': currentCategory === category.id }]" 
					  @click="switchCategory(category.id)">
				{{ category.name }}
			  </button>
			</view>
		  </scroll-view>
		   <scroll-view 
			scroll-y 
			class="video-container" 
			@scrolltolower="loadMore"
			>
			<!-- 待探索实验分类导航 -->
			<view class="category-levels" v-if="currentCategory === 1">
			  <!-- 面包屑导航 -->
			  <view class="category-breadcrumb" v-if="activeAwaitPath.length > 0">
			    <text class="breadcrumb-item" @click="goToAwaitLevel(0)">全部</text>
			    <text class="breadcrumb-separator">/</text>
			    <text 
			      v-for="(item, index) in activeAwaitPath" 
			      :key="index"
			      class="breadcrumb-item"
			      @click="goToAwaitLevel(index + 1)">
			      {{ item }}
			    </text>
			  </view>
			  <!-- 当前层级分类 -->
			  <scroll-view scroll-x class="category-scroll">
			    <view class="category-header">
			        <view 
			          v-for="(category, index) in getCurrentAwaitLevelCategories()" 
			          :key="index" 
			          :class="['category-view', { 
			            'active': isAwaitCategoryActive(category.name),
			            'disabled': category.disabled 
			          }]" 
			          @click="!category.disabled && selectAwaitCategory(category.name, currentCategoryLevel)">
			          {{ category.name }}
			        </view>
			      </view>
			  </scroll-view>
			</view>
			<!-- 待探索视频列表 -->
			<view class="video-grid" v-if="currentCategory === 1">
			  <view v-for="(video, index) in filteredAwaitVideos" :key="index" 
			        :class="['video-card', video.source === 'await' ? 'await-video' : 'completed-video']"
			        @click="playPolyvVideo(video)">
			    <view class="video-info">
			      <text class="video-title">{{ video.video_name }}</text>
			    </view>
			  </view>
			</view>
		  <!-- Polyv视频列表 -->
		  <view class="video-grid" v-if="currentCategory === 2">
			<view v-for="(video, index) in polyvVideos" :key="index" 
				  class="video-card" 
				  @click="playPolyvVideo(video)">
			  <image :src="video.cover || 'placeholder.png'" mode="aspectFill" class="video-thumbnail" />
			  <view class="video-info">
				<text class="video-title">{{ video.title }}</text>
				<view class="video-meta">
				  <text class="video-category">{{ video.cateName }}</text>
				  <text class="video-duration">{{ formatDuration(video.duration) }}</text>
				</view>
			  </view>
			</view>
		  </view>
		  <!-- 科学列表 -->
		  <view class="video-grid" v-if="currentCategory === 3">
		    <view v-for="(item, index) in scienceList" :key="index" 
		          class="video-card"
		  		@click="viewScience(item.id)">
		      <view class="video-info">
		        <text class="video-title">{{ item.title }}</text>
		      </view>
		    </view>
		  </view>
		  <view v-else class="empty-tip">
			<text></text>
		  </view>
		  </scroll-view>
		  <view v-if="pagination.isLoading" class="loading-more">
			<text>加载中...</text>
		  </view>
		  <view v-if="!pagination.hasMore && !pagination.isLoading" class="no-more">
			<text>没有更多视频了</text>
		  </view>
	  </view>
	</view>
  </template>
  
  <script>
  import { generateSignature } from '@/utils/auth'
  import md5 from 'md5'
  import sha1 from 'sha1'
  export default {
	data() {
	  return {
		swiperlist1: [],
		currentCategoryLevel: 1, // 当前分类层级
		activeCategoryPath: [], // 当前选中的分类路径
		hasNextLevel: true,
		selectedCategories: [], // 已选择的分类路
		activeAwaitPath: [], // 待探索实验的当前选中分类路径
		selectedAwaitCategories: [], // 待探索实验的已选择分类路径
		categories: [
		  { id: 1, name: '创新力实验' },
		  { id: 2, name: '探索奥秘' },
		  { id: 3, name: '世界科学史' }
		],
		currentCategory: 1,
		pagination: {
			  currentPage: 1,
			  totalPages: 1,
			  isLoading: false,
			  hasMore: true
			},
		isLoggedIn: false,  // 登录状态
		hasValidCard: false,
		username: "",       // 会员名
		polyvConfig: {
			  appId: 'gr14scwtlf',
			  appSecret: '0d7207f7844347a8b121582b251f3bd5',
			  secretKey: '1syhBlbscg',
			  userid: '2592d1c5f9',
			  cateId: '1745478294001',
			  playListIds: '1747616053722'
			},
		awaitVideos: [], //待探索实验
		completedVideos: [], //已完成实验
		scienceList: [],
		polyvVideos: [] // 存储从Polyv获取的视频列表
	  };
	},
	computed: {
		filteredAwaitVideos() {
		    if (!this.awaitVideos && !this.completedVideos) return [];
		    
		    // 合并待探索和已完成实验数据，并添加来源标记
		    const awaitWithSource = (this.awaitVideos || []).map(video => ({
		      ...video,
		      source: 'await'
		    }));
		    
		    const completedWithSource = (this.completedVideos || []).map(video => ({
		      ...video,
		      source: 'completed'
		    }));
		    
		    const allVideos = [...awaitWithSource, ...completedWithSource];
		    
		    return allVideos.filter(video => {
		      for (let i = 0; i < this.selectedAwaitCategories.length; i++) {
		        const levelKey = `category${i + 1}`;
		        if (video[levelKey] !== this.selectedAwaitCategories[i]) {
		          return false;
		        }
		      }
		      return true;
		    });
		  },
		filteredCompletedVideos() {
		    if (!this.completedVideos) return [];
		    return this.completedVideos.filter(video => {
		      for (let i = 0; i < this.selectedCategories.length; i++) {
		        const levelKey = `category${i + 1}`;
		        if (video[levelKey] !== this.selectedCategories[i]) {
		          return false;
		        }
		      }
		      return true;
		    });
		  }
	},
	methods: {
		isAwaitCategoryActive(categoryName) {
		    return this.activeAwaitPath.includes(categoryName);
		  },
		  
		  goToAwaitLevel(level) {
		    this.activeAwaitPath = this.activeAwaitPath.slice(0, level);
		    this.selectedAwaitCategories = [...this.activeAwaitPath];
		    this.currentCategoryLevel = level + 1;
		  },
		  
		  selectAwaitCategory(category, level) {
		    this.activeAwaitPath[level - 1] = category;
		    this.activeAwaitPath = this.activeAwaitPath.slice(0, level);
		    this.selectedAwaitCategories = [...this.activeAwaitPath];
		    this.currentCategoryLevel = level + 1;
		  },
		  
		  getCurrentAwaitLevelCategories() {
		    const allVideos = [...(this.awaitVideos || []), ...(this.completedVideos || [])];
		    if (!allVideos || allVideos.length === 0) return [];
		    
		    const levelKey = `category${this.currentCategoryLevel}`;
		    const categories = new Set();
		    
		    const hasNextLevel = allVideos.some(video => {
		      if (!this.isAwaitVideoInCurrentPath(video)) return false;
		      const nextLevelKey = `category${this.currentCategoryLevel + 1}`;
		      return video[nextLevelKey] !== undefined;
		    });
		    
		    this.hasNextLevel = hasNextLevel;
		    
		    allVideos.forEach(video => {
		      if (this.isAwaitVideoInCurrentPath(video)) {
		        categories.add(video[levelKey]);
		      }
		    });
		    
		    return Array.from(categories).map((name, index) => ({
		      id: index + 1,
		      name: name,
		      disabled: !hasNextLevel
		    }));
		  },
		  
		  isAwaitVideoInCurrentPath(video) {
		    for (let i = 0; i < this.selectedAwaitCategories.length; i++) {
		      const levelKey = `category${i + 1}`;
		      if (video[levelKey] !== this.selectedAwaitCategories[i]) {
		        return false;
		      }
		    }
		    return true;
		  },
		isCategoryActive(categoryName) {
		    return this.activeCategoryPath.includes(categoryName);
		  },
		  
		  goToCategoryLevel(level) {
		    this.activeCategoryPath = this.activeCategoryPath.slice(0, level);
		    this.selectedCategories = [...this.activeCategoryPath]; // 同步更新selectedCategories
		    this.currentCategoryLevel = level + 1;
		  },
		  
		  selectCategory(category, level) {
		    this.activeCategoryPath[level - 1] = category;
		    this.activeCategoryPath = this.activeCategoryPath.slice(0, level);
		    this.selectedCategories = [...this.activeCategoryPath]; // 确保selectedCategories同步更新
		    this.currentCategoryLevel = level + 1;
		  },
		  
		 getCurrentLevelCategories() {
		   if (!this.completedVideos || this.completedVideos.length === 0) return [];
		   
		   const levelKey = `category${this.currentCategoryLevel}`;
		   const categories = new Set();
		   
		   // 检查是否还有下一级分类
		   const hasNextLevel = this.completedVideos.some(video => {
		     if (!this.isVideoInCurrentPath(video)) return false;
		     const nextLevelKey = `category${this.currentCategoryLevel + 1}`;
		     return video[nextLevelKey] !== undefined;
		   });
		   
		   this.hasNextLevel = hasNextLevel; // 保存状态
		   
		   this.completedVideos.forEach(video => {
		     if (this.isVideoInCurrentPath(video)) {
		       categories.add(video[levelKey]);
		     }
		   });
		   
		   return Array.from(categories).map((name, index) => ({
		     id: index + 1,
		     name: name,
		     disabled: !hasNextLevel // 如果没有下一级则禁用
		   }));
		 },
		  
		  isVideoInCurrentPath(video) {
		    for (let i = 0; i < this.selectedCategories.length; i++) {
		      const levelKey = `category${i + 1}`;
		      if (video[levelKey] !== this.selectedCategories[i]) {
		        return false;
		      }
		    }
		    return true;
		  },
		  
	  // 获取token
		async getToken() {
		  try {
			const phone = uni.getStorageSync('userInfo')?.contactNumber || '';
			// 如果电话为空，不调用接口
			  if (!phone) {
				console.log('用户电话为空，跳过获取token');
				return null;
			  }
			const timestamp = Math.floor(Date.now() / 1000);
			const KEY = 'edsyn_manager_2025'; //
			const secret = md5(`${phone}${timestamp}${KEY}`);
			const res = await uni.request({
			  url: 'https://edsyn.xin/wx_api/create_token',
			  method: 'POST',
			  header: {
				'Content-Type': 'application/json'
			  },
			  data: {
				phone: phone,
				timestamp: timestamp,
				secret: secret
			  }
			});
			
			if (res.statusCode === 200) {
			  //console.log('token',res.data.data.token);
			  this.token = res.data.data.token;
			  uni.setStorageSync('token', res.data.data.token);
			  return res.data.data.token;
			} else {
			  uni.showToast({
				title: '获取token失败',
				icon: 'none'
			  });
			  return null;
			}
		  } catch (error) {
			console.error('获取token失败:', error);
			uni.showToast({
			  title: '获取token失败，请重试',
			  icon: 'none'
			});
			return null;
		  }
		},
		playPolyvVideo(video) {
			uni.navigateTo({
			  url: `/pages/video-player/video-player?video=${JSON.stringify(video)}`
			});
		  },
		  viewScience(scienceId) {
		  	uni.navigateTo({
		  	  url: `/pages/ScienceDetails/ScienceDetails?scienceId=${JSON.stringify(scienceId)}`
		  	});
		    },
		  async fetchScienceList() {
		    try {
		      const timestamp = Math.floor(Date.now() / 1000);
		      const nonce = Math.random().toString(36).substring(2, 10);
		      const signature = generateSignature(timestamp, nonce);
		      
		      const res = await uni.request({
		        url: 'https://aikexiaozhan.com/api/getScience',
		        method: 'POST',
		        header: {
		          'X-Timestamp': timestamp,
		          'X-Nonce': nonce,
		          'X-Signature': signature,
		          'Content-Type': 'application/json'
		        },
		        timeout: 5000
		      });
		      
		      if (res.statusCode === 200) {
		        this.scienceList = res.data.data;
		      }
		    } catch (error) {
		      console.error('获取science数据失败:', error);
		    }
		  },
	   async fetchPolyvVideos() {
		 try {
		   const timestamp = Date.now();
		   const signStr = `playListIds=${this.polyvConfig.playListIds}&ptime=${timestamp}&userid=${this.polyvConfig.userid}${this.polyvConfig.secretKey}`;
		   const sign = sha1(signStr).toUpperCase();
		   //console.log('signStr:', signStr);
		   //console.log('sign:', sign);
		   const res = await uni.request({
			 url: 'https://api.polyv.net/v2/play-list/list',
			 method: 'GET',
			 data: {
			   userid: this.polyvConfig.userid,
			   ptime: timestamp,
			   sign: sign,
			   playListIds: this.polyvConfig.playListIds
			 }
		   });
		   
		   if (res.data.code === 200) {
			// 处理新的嵌套数据结构
			const newVideos = res.data.data.contents.flatMap(content => 
			  content.videoList.map(video => ({
				vid: `${video.vid}_2`,
				title: video.title,
				cateName: video.cateName,
				cover: video.miniCoverURL,
				duration: video.duration
			  }))
			);
			
			this.polyvVideos = newVideos;
		   }else{
			   console.log('获取视频失败，状态码:', res.statusCode, '响应:', res.data);
		   }
		 } catch (error) {
		   console.error('获取Polyv视频失败:', error);
		   this.pagination.isLoading = false;
		 }
	   },
	  // 获取视频列表
		  async fetchVideoCompleteList() {
			try {
			  const phone = uni.getStorageSync('userInfo')?.contactNumber || '';
			  const token = uni.getStorageSync('token') || '';
			  
			  if (!phone || !token) {
				console.log('缺少必要参数');
				return;
			  }
  
			  const res = await uni.request({
				url: 'https://edsyn.xin/wx_api/video_complete_list',
				method: 'POST',
				header: {
				  'Content-Type': 'application/json'
				},
				data: {
				  phone: phone,
				  token: token
				}
			  });
			  
			  if (res.statusCode === 200 && res.data.code === '200') {
				this.completedVideos = res.data.data;
			  } else {
				uni.showToast({
				  title: '请先在学习桌APP上注册',
				  icon: 'none'
				});
			  }
			} catch (error) {
			  console.error('获取视频列表失败:', error);
			  uni.showToast({
				title: '获取视频列表失败，请重试',
				icon: 'none'
			  });
			}
		  },
		  async fetchVideoAwaitList() {
		  			try {
		  			  const phone = uni.getStorageSync('userInfo')?.contactNumber || '';
		  			  const token = uni.getStorageSync('token') || '';
		  			  
		  			  if (!phone || !token) {
		  				console.log('缺少必要参数');
		  				return;
		  			  }
		    
		  			  const res = await uni.request({
		  				url: 'https://edsyn.xin/wx_api/video_await_list',
		  				method: 'POST',
		  				header: {
		  				  'Content-Type': 'application/json'
		  				},
		  				data: {
		  				  phone: phone,
		  				  token: token
		  				}
		  			  });
		  			  
		  			  if (res.statusCode === 200 && res.data.code === '200') {
		  				this.awaitVideos = res.data.data;
		  			  } else {
		  				uni.showToast({
		  				  title: '请先在学习桌APP上注册',
		  				  icon: 'none'
		  				});
		  			  }
		  			} catch (error) {
		  			  console.error('获取视频列表失败:', error);
		  			  uni.showToast({
		  				title: '获取视频列表失败，请重试',
		  				icon: 'none'
		  			  });
		  			}
		  },
		  async checkMemberCardStatus() {
		  	  try {
		  		const userInfo = uni.getStorageSync('userInfo');
		  		if (!userInfo?.contactNumber) return;
		  		
		  		const timestamp = Math.floor(Date.now() / 1000);
		  		const nonce = Math.random().toString(36).substring(2, 10);
		  		const signature = generateSignature(timestamp, nonce);
		  		
		  		const res = await uni.request({
		  		  url: 'https://aikexiaozhan.com/api/checkMemberCardByPhone',
		  		  method: 'POST',
		  		  header: {
		  			'X-Timestamp': timestamp,
		  			'X-Nonce': nonce,
		  			'X-Signature': signature,
		  			'Content-Type': 'application/json'
		  		  },
		  		  data: {
		  			phoneNumber: userInfo.contactNumber
		  		  }
		  		});
		  		
		  		if (res.statusCode === 200 && res.data.code === 200) {
		  		  this.hasValidCard = res.data.hasValidCard;
		  		}
		  	  } catch (error) {
		  		console.error('检查会员卡状态失败:', error);
		  	  }
		  	},
	  // 获取 swiper 数据
	  async fetchSwiperData(category, listName) {
		// 先从本地存储获取数据
		  const storageKey = `swiper_${category}`;
		  const cachedData = uni.getStorageSync(storageKey);
		  
		  // 如果有缓存数据且未过期，直接使用
		  if (cachedData) {
			const { data, timestamp } = cachedData;
			// 设置缓存有效期为1小时
			if (Date.now() - timestamp < 3600000) {
			  this[listName] = data;
			  return;
			}
		  }
		this.loading = true;
		try {
		  const timestamp = Math.floor(Date.now() / 1000);
		  const nonce = Math.random().toString(36).substring(2, 10);
		  const signature = generateSignature(timestamp, nonce);
		  
		  const res = await uni.request({
			url: `https://aikexiaozhan.com/api/swiper/${category}`,
			method: "POST",
			header: {
			  'X-Timestamp': timestamp,
			  'X-Nonce': nonce,
			  'X-Signature': signature,
			  'Content-Type': 'application/json'
			},
			timeout: 5000  // 添加超时设置
		  });
		  
		  if (res.statusCode === 200) {
			this[listName] = res.data;
			// 将数据存入本地存储
			  uni.setStorageSync(storageKey, {
				data: res.data,
				timestamp: Date.now()
			  });
		  } else {
			uni.showToast({
			  title: `获取${category}数据失败`,
			  icon: 'none'
			});
		  }
		} catch (error) {
		  console.error(`请求${category}失败`, error);
		  uni.showToast({
			title: '网络请求失败，请稍后重试',
			icon: 'none'
		  });
		} finally {
		  this.loading = false;
		}
	  },  
	  // 优化后的页面跳转方法log
	  goToPage(listType, pageIndex) {
		const currentList = this[listType];
		const currentItem = currentList[pageIndex];
		if (!currentItem?.adId) {
			uni.showToast({ title: '无效的页面链接', icon: 'none' });
			return;
		}
		
		// 添加防抖
		if (this.lastClickTime && Date.now() - this.lastClickTime < 1000) return;
		this.lastClickTime = Date.now();
	  
		// 统一跳转到广告详情页，携带adId参数
		uni.navigateTo({
			url: `/pages/AdvertisingDetails/AdvertisingDetails?adId=${currentItem.adId}`
		});
	  },  
	  // 跳转到注册页面
	  goToRegister() {
		uni.navigateTo({
		  url: '/pages/register/register' // 注册页面路径
		});
	  },
	  // 切换视频分类
	  switchCategory(categoryId) {
	    this.currentCategory = categoryId;
		if (categoryId === 1) {
		  this.currentCategoryLevel = 1;
		  this.activeAwaitPath = []; // 重置待探索实验路径
		  this.selectedAwaitCategories = []; // 重置待探索实验选择
		  this.fetchVideoAwaitList();
		  this.fetchVideoCompleteList(); 
		}
	    if (categoryId === 2) {
		  this.fetchPolyvVideos();
	    }
	    if (categoryId === 3) {
			this.fetchScienceList();
	    }
	  },
	  formatDuration(seconds) {
		  if (!seconds) return '';
		  const mins = Math.floor(seconds / 60);
		  const secs = seconds % 60;
		  return `${mins}:${secs < 10 ? '0' + secs : secs}`;
		},
	  loadMore() {
		  if (!this.pagination.isLoading && this.pagination.hasMore) {
  
		  }
		},
	  // 检查用户登录状态
	  checkLoginStatus() {
		const userInfo = uni.getStorageSync('userInfo');
		if (userInfo) {
		  this.isLoggedIn = true;
		  this.username = userInfo.parentName;
		} else {
		  this.isLoggedIn = false;
		}
	  }
	},
	mounted() {
	  // 初始化时检查登录状态
	  this.checkLoginStatus();
	  if (!this.isLoggedIn) {
		  uni.redirectTo({
			url: '/pages/register/register'
		  });
		  return;
		}
	  this.fetchSwiperData("swiperlist1", "swiperlist1");
	  this.getToken().then(() => {
	  			this.fetchVideoAwaitList();
				this.fetchVideoCompleteList(); 
	  		  });
	  this.currentCategory = 1;
	  this.checkMemberCardStatus();
	},
  };
  </script>
  
  <style scoped>
.category-view {
  display: inline-block;
  margin: 0 5px;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  color: #333;
  background-color: #f5f5f5;
  cursor: pointer;
}

.category-view.active {
  color: white;
  background-color: #F7664A;
}

.category-view.disabled {
  opacity: 0.5;
  pointer-events: none;
}	  
  .empty-tip {
	padding: 20px;
	text-align: center;
	color: #666;
	font-size: 16px;
  }	
  .header-title {
	width: 100%;
	height: 132px; /* 调整为与小程序胶囊按钮相同高度 */
	line-height: 132px; /* 确保文字垂直居中 */
	font-family: PingFang-SC, PingFang-SC;
	font-weight: 500;
	font-size: 16px;
	color: #FFFFFF;
	text-align: left;
	font-style: normal;
	text-transform: none;
	background-color: #F7664A;
	padding: 0 10px; /* 只保留左右padding */
	margin: 0 auto;
	box-sizing: border-box; /* 确保padding不影响总高度 */
  }
  .category-scroll {
	width: 100%;
	white-space: nowrap;
	padding: 10px 0;
  }
  
  /* 视频网格布局 */
  .video-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
	gap: 15px;
	padding: 15px;
  }
	.category-levels {
	  margin-bottom: 15px;
	  background-color: #f8f8f8;
	  border-radius: 8px;
	  padding: 10px;
	}

.category-breadcrumb {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  margin-bottom: 15px;
}

.breadcrumb-item {
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
  background-color: transparent;
  transition: all 0.2s;
}
.breadcrumb-item:active {
  background-color: #f0f0f0;
}
.breadcrumb-item:hover {
  background-color: #F7664A;
  color: white;
}

.breadcrumb-separator {
  margin: 0 5px;
  color: #999;
}
  /* 视频卡片样式 */
  .video-card {
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	transition: transform 0.3s;
	background-color: #F7664A;
  }

/* 待探索实验视频背景色 - 红色 */
.video-card.await-video {
  border-left: 4px solid #f44336;
}

/* 已完成实验视频背景色 - 浅蓝色 */
.video-card.completed-video {
  border-left: 4px solid #2196f3;
}
  
  .category-button.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  .category-button:hover:not(.active) {
    background-color: rgba(247, 102, 74, 0.1);
  }
  .video-card:hover {
	transform: translateY(-5px);
  }
  
  .video-thumbnail {
	width: 100%;
	height: 100px;
	object-fit: cover;
  }
  
  .video-title {
	font-size: 14px;
	font-weight: bold;
	color: #fff;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	  line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
  }
  
  .video-meta {
	display: flex;
	justify-content: space-between;
	margin-top: 5px;
	font-size: 12px;
	color: #fff;
  }	
  .swiper-container {
	height: 200px;
	position: relative; /* 添加相对定位 */
	top: -30px; /* 上移30px与标题重叠 */
	margin-top: 0; /* 移除原有上边距 */
	width: calc(100% - 20px);
	margin: 0 10px;
  }
  
  .swiper-item {
	height: 100%;
	position: relative;
	width: 100%; /* 添加宽度100% */
  }
  
  .swiper-image {
	width: 100%;
	height: 100%;
  }
  
  .swiper-title {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	color: white;
	text-align: left;
	background-color: rgba(0, 0, 0, 0.5);
	padding: 10px;
	font-size: 16px; /* 添加字体大小 */
	white-space: nowrap; /* 防止标题换行 */
	overflow: hidden;
	text-overflow: ellipsis; /* 超出部分显示省略号 */
  }		
  
  /* 视频分类按钮 */
.category-header {
  display: flex;
  gap: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 15px;
}
  
  .category-button {
	padding: 10px;
	width: 225rpx;
	height: 83rpx;
	background-color: #f8f8f8;
	color: #F7664A;
	border: 1px solid #F7664A;
	cursor: pointer;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 16px;
	font-weight: 500;
	border-radius: 42rpx;
  }
  
.category-button.active {
  background-color: #F7664A;
  color: white;
  box-shadow: 0 2px 5px rgba(247, 102, 74, 0.3);
}
  
  
  .video-info {
	padding: 12px;
  }
  
  
  .loading-more {
	padding: 20px 0;
	text-align: center;
  }
  
  .loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
  }
  
  .loading-icon {
	width: 40px;
	height: 40px;
	margin-bottom: 10px;
  }
  
  .loading-text {
	font-size: 14px;
	color: #999;
  }
  
  .no-more {
	padding: 20px 0;
	text-align: center;
  }
  
  .no-more-text {
	font-size: 14px;
	color: #999;
	position: relative;
  }
  
  .no-more-text::before,
  .no-more-text::after {
	content: "";
	position: absolute;
	top: 50%;
	width: 30px;
	height: 1px;
	background-color: #eee;
  }
  
  .no-more-text::before {
	left: -40px;
  }
  
  .no-more-text::after {
	right: -40px;
  }
  </style>
  