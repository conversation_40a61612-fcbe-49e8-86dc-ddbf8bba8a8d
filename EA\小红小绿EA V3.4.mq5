//+------------------------------------------------------------------+
//|                                                         小红小绿 |
//|                                         Copyright 2025, notandor |
//|                                          https://www.notandor.cn |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, notandor"
#property link      "https://www.notandor.cn"
#property version   "3.4"
#property description "《小红小绿》\n小红垂首低吟处，小绿昂头高唱时。\n烛影浮沉藏大道，阴阳涨落寸心知。"
#property strict
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh> 
#include <Trade\OrderInfo.mqh> 
#include <Arrays\ArrayLong.mqh> 
#include <Arrays\ArrayDouble.mqh>
// 定义交易模式全局输入变量
enum TradeMode
{
    TRADE_BOTH,   // 多空交易
    TRADE_PAUSE       // 暂停加单
};

input TradeMode tradeMode = TRADE_BOTH; // 默认多空交易
input double lotSize = 0.01;//手数
input int takeProfitPoints=0;//止盈点
input int stopLossPoints=600;//止损点>0(参考震荡区间差)
input bool breakevenStopLoss = true;//是否启用保本止损
input int profittoStopLoss = 600;//盈利多少点开始保本
input int breakevenBuffer = 30; //盈利保本缓冲点数
input int equityIncrement = 600;//起平增量，0=无限制
input int maxLossThreshold = 300; //亏损停加，0=无限制
input int TotalStopLoss = 600;//总止损，0=无限制
input int MagicNumber = 999;//魔术号
input string CommentString="小红小绿 V3.4"; //备注

int startIndex = 240; // 从当前K线往回追溯N根
double maxGreenPrice = -DBL_MAX; // 小绿最大值
double minRedPrice = DBL_MAX;   // 小红最小值
bool tradingLineEnabled = false; // 是否允许交易
// 存储小红和小绿的名称数组
string redLineNames[];
string greenLineNames[];
datetime lastProcessedTime = 0; // 上一次处理的K线时间
int redLineCount=0;//小红数量
int greenLineCount=0;//小绿数量
double accountBalance = 0; // 本金
double accountEquity = 0;   // 净值    
double totalBuyVolume = 0.0;           // 当前所有买单的总手数
double totalSellVolume = 0.0;          // 当前所有卖单的总手数
int buyCount = 0;               // 买单个数
int sellCount = 0;              // 卖单个数
string buttonName = "CloseAllButton";
string closeHalfButtonName = "CloseHalfButton"; 
double currentProfit = 0.0;       // 总盈亏金额
double historyProfit = 0.0;       // 历史盈亏金额
bool maxLossReached = false; 


//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
void DeleteAllChartObjects()
{
    long chartId = ChartID();
    int totalObjects = ObjectsTotal(chartId);
    for(int x = totalObjects - 1; x >= 0; x--) {
        string objName = ObjectName(chartId,x);
        if(objName != "") ObjectDelete(chartId,objName);
    }
}
int OnInit()
{
   // 确保足够的数据
   if (Bars(_Symbol,_Period) < startIndex)
   {
      Print("K线数量不足，无法执行");
      return INIT_FAILED;
   }
   tradingLineEnabled = false;
   // 获取当前图表的对象总数
   DeleteAllChartObjects();
   // 动态调整大小为 0
   ArrayResize(redLineNames, 0);
   ArrayResize(greenLineNames, 0);   

   // 遍历第N根到第1根K线
   for (int i = startIndex; i >= 1; i--)
   {
      // 如果是阴线，则画小红
      if (iClose(_Symbol, PERIOD_CURRENT, i) < iOpen(_Symbol, PERIOD_CURRENT, i)) // 阴线
      {
         DrawRedLine(i, iLow(_Symbol, PERIOD_CURRENT, i));
      }
      // 如果是阳线，则画小绿
      if (iClose(_Symbol, PERIOD_CURRENT, i)> iOpen(_Symbol, PERIOD_CURRENT, i)) // 阳线
      {
         DrawGreenLine(i, iHigh(_Symbol, PERIOD_CURRENT, i));
      }
   }
   CreateButton(); // 创建切换按钮
   return INIT_SUCCEEDED;
}
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   DeleteAllChartObjects();
}
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
//---
   if(stopLossPoints<=0){
      Print("请设置合适的止损点");
      return;
   }
   accountBalance = AccountInfoDouble(ACCOUNT_BALANCE); // 本金
   accountEquity =  AccountInfoDouble(ACCOUNT_EQUITY);   // 净值    
   tradingLineEnabled = true;
   // 获取最新K线的时间戳
   datetime latestTime = iTime(_Symbol, PERIOD_CURRENT, 1);   
   // 检查是否为新K线
   if (latestTime != lastProcessedTime)
   {
      lastProcessedTime = latestTime; // 更新已处理时间 
      if (iClose(_Symbol, PERIOD_CURRENT, 1)  < iOpen(_Symbol, PERIOD_CURRENT, 1)) // 阴线
      {
         DrawRedLine(1, iLow(_Symbol, PERIOD_CURRENT, 1));
      }
      else if (iClose(_Symbol, PERIOD_CURRENT, 1) > iOpen(_Symbol, PERIOD_CURRENT, 1)) // 阳线
      {
         DrawGreenLine(1, iHigh(_Symbol, PERIOD_CURRENT, 1));
      }
      redLineCount=ArraySize(redLineNames);//小红数量
      greenLineCount=ArraySize(greenLineNames);//小绿数量  
   }  
   GetOrderStats();//统计订单信息
   CSSComent();//显示订单信息
   if(breakevenStopLoss)SetBreakevenStopLoss(profittoStopLoss);//保本止损   
   CalculateFunds(); 
   if(equityIncrement > 0)CloseProfitableOrders(equityIncrement);//自动平仓      
   if(maxLossThreshold > 0 && currentProfit <= -maxLossThreshold && !maxLossReached) {
        maxLossReached = true;
        Print("达到亏损阈值，交易功能已禁用");
    }
    if(TotalStopLoss>0 && currentProfit <= -TotalStopLoss){
      CloseAllOrders();
      Print("达到总止损，停止交易");
      ExpertRemove();  // 移除当前的EA
    }

}
// 绘制小红并管理小红逻辑
void DrawRedLine(int index, double lowPrice)
{
   string lineName = "RedTrendLine_" + TimeToString(iTime(_Symbol, PERIOD_CURRENT, index), TIME_DATE | TIME_MINUTES);
   // 检查同一时间是否已经存在红线
   if (ObjectFind(0, lineName) != -1)
      return; // 如果已经存在，不重复绘制 
   // 判断周期，仅在指定周期绘制红线     
   datetime startTime = iTime(_Symbol, PERIOD_CURRENT, index);
   datetime endTime = iTime(_Symbol, PERIOD_CURRENT, 0); // 水平向右延伸到最新的K线时间   
   // 创建趋势线
   ObjectCreate(0, lineName, OBJ_TREND, 0, startTime, lowPrice, endTime, lowPrice);
   ObjectSetInteger(0, lineName, OBJPROP_COLOR, clrRed);
   ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 2);
   ObjectSetInteger(0, lineName, OBJPROP_RAY_RIGHT, true); 

   // 存储红线的名称和价格
   ArrayResize(redLineNames, ArraySize(redLineNames) + 1);
   redLineNames[ArraySize(redLineNames) - 1] = lineName;

   // 更新阈值为当前阴线的最低价
   double thresholdPrice = lowPrice;
   int validCount = 0; // 有效红线计数
   minRedPrice = DBL_MAX; // 重置最小值，每次重新计算
   long chartId = ChartID();
   // 遍历小红，如果当前动态阈值小于小红的价格，则删除该红线
   for (int j = 0; j < ArraySize(redLineNames); j++)
   {
      double redLinePrice = ObjectGetDouble(chartId, redLineNames[j], OBJPROP_PRICE);
      datetime redLineTime = (datetime)ObjectGetInteger(chartId, redLineNames[j], OBJPROP_TIME);
      if (redLineTime < iTime(_Symbol, PERIOD_CURRENT, startIndex) || thresholdPrice < redLinePrice)
      {
         ObjectDelete(chartId, redLineNames[j]); // 删除小红        
         if(tradingLineEnabled && tradeMode==TRADE_BOTH && !maxLossReached)
         {
             ExecuteOrder(ORDER_TYPE_SELL, lotSize, takeProfitPoints, stopLossPoints);
         }
      }else
      {
         // 将有效红线移动到数组前部
         redLineNames[validCount++] = redLineNames[j];    
         // 更新最小值
         if (redLinePrice < minRedPrice)minRedPrice = redLinePrice;
      }
   }
   // 调整数组大小，保留有效红线
   ArrayResize(redLineNames, validCount);   
}

// 绘制小绿并管理小绿逻辑
void DrawGreenLine(int index, double highPrice)
{
 
   string lineName = "GreenTrendLine_" + TimeToString(iTime(_Symbol, PERIOD_CURRENT, index), TIME_DATE | TIME_MINUTES);
   // 检查同一时间是否已经存在绿线
   if (ObjectFind(0, lineName) != -1)
      return; // 如果已经存在，不重复绘制   
   // 判断周期，仅在指定周期绘制绿线       
   datetime startTime = iTime(_Symbol, PERIOD_CURRENT, index);
   datetime endTime = iTime(_Symbol, PERIOD_CURRENT, 0); // 水平向右延伸到最新的K线时间
   // 创建趋势线
   ObjectCreate(0, lineName, OBJ_TREND, 0, startTime, highPrice, endTime, highPrice);
   ObjectSetInteger(0, lineName, OBJPROP_COLOR, clrGreen);
   ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 2);   
   ObjectSetInteger(0, lineName, OBJPROP_RAY_RIGHT, true); 

   // 存储小绿的名称和价格
   ArrayResize(greenLineNames, ArraySize(greenLineNames) + 1);
   greenLineNames[ArraySize(greenLineNames) - 1] = lineName;

   // 更新阈值为当前阳线的最高价
   double thresholdPrice = highPrice;
   int validCount = 0; // 有效绿线计数   
   maxGreenPrice = -DBL_MAX; // 重置最大值，每次重新计算
   long chartId = ChartID();
   // 遍历小绿，如果当前动态阈值大于小绿的价格，则删除该小绿
   for (int k = 0; k < ArraySize(greenLineNames); k++)
   {
      double greenLinePrice = ObjectGetDouble(chartId, greenLineNames[k], OBJPROP_PRICE);
      datetime greenLineTime = (datetime)ObjectGetInteger(chartId, greenLineNames[k], OBJPROP_TIME);
      if (greenLineTime < iTime(_Symbol, PERIOD_CURRENT, startIndex) || thresholdPrice > greenLinePrice)
      {
         ObjectDelete(chartId, greenLineNames[k]); // 删除小绿   
         if(tradingLineEnabled && tradeMode==TRADE_BOTH && !maxLossReached)
         {
             ExecuteOrder(ORDER_TYPE_BUY, lotSize, takeProfitPoints, stopLossPoints);         
         }
      }else
      {
         // 将有效绿线移动到数组前部
         greenLineNames[validCount++] = greenLineNames[k];     
         // 更新最大值
         if (greenLinePrice > maxGreenPrice)maxGreenPrice = greenLinePrice;                            
      }
   }

   // 调整数组大小，保留有效红线
   ArrayResize(greenLineNames, validCount);   
}  

//| 统一订单执行函数                                                |
//+------------------------------------------------------------------+
void ExecuteOrder(ENUM_ORDER_TYPE orderType, double volume, int takeProfitPips, int stopLossPips)
{
    
    if(accountEquity <= 0)
      {
          Print("净值不足，无法开仓");
          return;
      }
    // 价格计算
    double price = (orderType == ORDER_TYPE_BUY) ? 
                 SymbolInfoDouble(_Symbol, SYMBOL_ASK) : 
                 SymbolInfoDouble(_Symbol, SYMBOL_BID);
    price = NormalizeDouble(price, _Digits);

    // 计算止损止盈
    double stopLoss = 0.0, takeProfit = 0.0;
    if(stopLossPips > 0) {
        stopLoss = NormalizeDouble(price + (orderType == ORDER_TYPE_SELL ? 1 : -1) * stopLossPips * _Point, _Digits);
    }
    
    if(takeProfitPips > 0) {
        takeProfit = NormalizeDouble(price + (orderType == ORDER_TYPE_BUY ? 1 : -1) * takeProfitPips * _Point, _Digits);
    }

    // 填充交易请求结构体
    MqlTradeRequest request = {};
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = volume;
    request.type = orderType;
    request.price = price;
    request.sl = stopLoss;
    request.tp = takeProfit;
    request.deviation = 10;
    request.magic = MagicNumber;
    request.comment = CommentString;

      // 检查交易品种支持的填充类型
      int filling_mode = (int)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE); // 获取支持的填充模式
      //Print("Filling mode for ", _Symbol, ": ", filling_mode);
      
      // 检查填充模式支持
      if ((filling_mode & SYMBOL_FILLING_FOK) == SYMBOL_FILLING_FOK)
      {
          request.type_filling = ORDER_FILLING_FOK;  // 完全成交或取消
          //Print("Using FOK mode");
      }
      else if ((filling_mode & SYMBOL_FILLING_IOC) == SYMBOL_FILLING_IOC)
      {
          request.type_filling = ORDER_FILLING_IOC;  // 立即成交或取消
          //Print("Using IOC mode");
      }
      else
      {
          request.type_filling = ORDER_FILLING_RETURN;  // 默认使用 RETURN 模式
          //Print("Using RETURN mode");
      }

    // 发送订单
    MqlTradeResult result;
    if(!OrderSend(request, result)) {
        string errMsg = StringFormat("%s订单失败 代码:%d 信息:%s", 
                   EnumToString(orderType), 
                   result.retcode,
                   ErrorDescription(result.retcode));
       Print(errMsg);             
    }
}

//| 自定义错误描述函数                                              |
//+------------------------------------------------------------------+
string ErrorDescription(int error_code)
{
    switch(error_code)
    {
        case 0:     return("没有错误");
        case 1:     return("没有错误，但结果未知");
        case 2:     return("通用错误");
        case 3:     return("无效参数");
        case 4:     return("交易服务器繁忙");
        // 补充其他常见错误码...
        case 130:   return("无效止损");
        case 131:   return("无效止盈");
        case 132:   return("无效交易量");
        case 133:   return("交易被禁止");
        case 146:   return("交易上下文繁忙");
        default:    return("未知错误");
    }
}

//+------------------------------------------------------------------+
//| 快速关闭所有持仓订单                                           |
//+------------------------------------------------------------------+
void CloseAllOrders()
{
    CTrade         m_trade;      // trading object
    CPositionInfo  m_position;   // position info object
    COrderInfo     m_order;      // order info object
    CArrayLong     m_arr_tickets; // array tickets

    m_trade.SetDeviationInPoints(INT_MAX);
    m_trade.SetAsyncMode(true);
    m_trade.SetMarginMode();
    m_trade.LogLevel(LOG_LEVEL_ERRORS);

    bool result = true;
    m_arr_tickets.Shutdown();

    // Close positions matching the target Magic Number and Symbol
    for (int i = 0; i < PositionsTotal(); i++)
    {
        if (!m_position.SelectByIndex(i))
        {
            PrintFormat("> Error: selecting position with index #%d failed. Error Code: %d", i, GetLastError());
            result = false;
            continue;
        }

        // Check if the position's magic number and symbol match the target
        if (m_position.Magic() == MagicNumber && m_position.Symbol() == _Symbol)
        {
            // Add position ticket to the array
            if (!m_arr_tickets.Add(m_position.Ticket()))
            {
                PrintFormat("> Error: adding position ticket #%I64u failed.", m_position.Ticket());
                result = false;
                continue;
            }
        }
    }

    // Process positions
    for (int i = 0; i < m_arr_tickets.Total(); i++)
    {
        ulong m_curr_ticket = m_arr_tickets.At(i);
        if (!m_position.SelectByTicket(m_curr_ticket))
        {
            PrintFormat("> Error: selecting position ticket #%I64u failed. Error Code: %d", m_curr_ticket, GetLastError());
            result = false;
            continue;
        }

        m_trade.SetExpertMagicNumber(m_position.Magic());
        m_trade.SetTypeFillingBySymbol(m_position.Symbol());

        if (m_trade.PositionClose(m_position.Ticket()) && (m_trade.ResultRetcode() == TRADE_RETCODE_DONE || m_trade.ResultRetcode() == TRADE_RETCODE_PLACED))
        {
            PrintFormat("Position ticket #%I64u on %s closed successfully.", m_position.Ticket(), m_position.Symbol());
            PlaySound("expert.wav");
        }
        else
        {
            PrintFormat("> Error: closing position ticket #%I64u on %s failed. Retcode=%u (%s)", m_position.Ticket(), m_position.Symbol(), m_trade.ResultRetcode(), m_trade.ResultComment());
            result = false;
        }
    }

    // Delete pending orders matching the target Magic Number and Symbol
    m_arr_tickets.Shutdown();
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (!m_order.SelectByIndex(i))
        {
            PrintFormat("> Error: selecting order with index #%d failed. Error Code: %d", i, GetLastError());
            result = false;
            continue;
        }

        // Check if the order's magic number and symbol match the target
        if (m_order.Magic() == MagicNumber && m_order.Symbol() == _Symbol)
        {
            // Add order ticket to the array
            if (!m_arr_tickets.Add(m_order.Ticket()))
            {
                PrintFormat("> Error: adding order ticket #%I64u failed.", m_order.Ticket());
                result = false;
                continue;
            }
        }
    }

    // Process orders
    for (int i = 0; i < m_arr_tickets.Total(); i++)
    {
        ulong m_curr_ticket = m_arr_tickets.At(i);
        if (!m_order.Select(m_curr_ticket))
        {
            PrintFormat("> Error: selecting order ticket #%I64u failed. Error Code: %d", m_curr_ticket, GetLastError());
            result = false;
            continue;
        }

        if (m_trade.OrderDelete(m_order.Ticket()) && (m_trade.ResultRetcode() == TRADE_RETCODE_DONE || m_trade.ResultRetcode() == TRADE_RETCODE_PLACED))
        {
            PrintFormat("Order ticket #%I64u on %s deleted successfully.", m_order.Ticket(), m_order.Symbol());
            PlaySound("expert.wav");
        }
        else
        {
            PrintFormat("> Error: deleting order ticket #%I64u on %s failed. Retcode=%u (%s)", m_order.Ticket(), m_order.Symbol(), m_trade.ResultRetcode(), m_trade.ResultComment());
            result = false;
        }
    }

    // If everything was successful, play sound and log completion
    if (result)
    {
        PlaySound("done.wav");
        //Print("Specified positions and pending orders have been successfully closed and deleted.");
    }
    else
    {
        PlaySound("timeout.wav");
        Print("Some operations failed. Please check the logs for errors.");
    }
}

//保本止损，盈得profitThreshold点后开始设置
void SetBreakevenStopLoss(int profitThreshold)
{
    CTrade trade;

    // 获取当前所有持仓数量
    int totalPositions = PositionsTotal();


    // 遍历所有持仓
    for (int i = totalPositions - 1; i >= 0; i--)
    {
        // 获取当前持仓的票号
        ulong ticket = PositionGetTicket(i);

        // 选择持仓
        if (!PositionSelectByTicket(ticket))
        {
            Print("无法选择持仓，票号：", ticket);
            continue;
        }

        // 获取当前持仓的类型
        int positionType = (int)PositionGetInteger(POSITION_TYPE);

        // 获取开仓价格、当前止损和当前报价
        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentStopLoss = PositionGetDouble(POSITION_SL);
        double currentPrice = (positionType == POSITION_TYPE_BUY) ? 
                              SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                              SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                              
        // 获取当前持仓的魔术号和品种
        int positionMagicNumber = (int)PositionGetInteger(POSITION_MAGIC);
        string positionSymbol = PositionGetString(POSITION_SYMBOL);

        // 如果持仓的魔术号或品种不匹配目标魔术号或品种，则跳过该持仓
        if (positionMagicNumber != MagicNumber || positionSymbol != _Symbol)
        {
            continue;  // 只处理目标魔术号和目标品种的订单
        }                              

        // 计算当前盈利点数
        double currentProfitPoints = (positionType == POSITION_TYPE_BUY) ? 
                                     (currentPrice - openPrice) / _Point : 
                                     (openPrice - currentPrice) / _Point;

        // 判断是否达到盈利阈值
        if (currentProfitPoints >= profitThreshold)
        {
            // 计算保本止损价格
            double breakevenPrice = (positionType == POSITION_TYPE_BUY) ? 
                                    NormalizeDouble(openPrice + breakevenBuffer * _Point, _Digits) : 
                                    NormalizeDouble(openPrice - breakevenBuffer * _Point, _Digits);


            // **只有当目标止损更接近现价时才修改止损**
            if ((positionType == POSITION_TYPE_BUY && breakevenPrice > currentStopLoss) || (positionType == POSITION_TYPE_SELL && breakevenPrice < currentStopLoss) || currentStopLoss == 0.0)
            {
                if (!trade.PositionModify(ticket, breakevenPrice, PositionGetDouble(POSITION_TP)))
                {
                    Print("设置保本止损失败，订单号：", ticket, " 错误代码：", GetLastError());
                }
                else
                {
                    //Print("已设置保本止损，订单号：", ticket, " 新止损价格：", breakevenPrice);
                }
            }
        }
    }
}
// 获取订单统计信息
void GetOrderStats()
{
    // 重置统计数据
    totalBuyVolume = 0.0;
    totalSellVolume = 0.0;
    buyCount = 0;
    sellCount = 0;

    // 获取当前所有持仓
    int totalPositions = PositionsTotal();

    for (int i = 0; i < totalPositions; i++)
    {
        ulong ticket = PositionGetTicket(i);
        if (PositionSelectByTicket(ticket)) // 选择当前持仓
        {
            // 检查魔术号和符号
            if (PositionGetInteger(POSITION_MAGIC) == MagicNumber && PositionGetString(POSITION_SYMBOL) == _Symbol)
            {
                int orderType = (int)PositionGetInteger(POSITION_TYPE); // 获取订单类型
                double orderLots = PositionGetDouble(POSITION_VOLUME); // 获取订单手数

                // 计算买单总手数和数量
                if (orderType == POSITION_TYPE_BUY)
                {
                    totalBuyVolume += orderLots;
                    buyCount++;
                }
                // 计算卖单总手数和数量
                else if (orderType == POSITION_TYPE_SELL)
                {
                    totalSellVolume += orderLots;
                    sellCount++;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+


void CreateButton()
{ 
    if (ObjectFind(0, buttonName) == -1) // 如果对象不存在，则创建
    {
        ObjectCreate(0, buttonName, OBJ_BUTTON, 0, 0, 0);
        ObjectSetString(0, buttonName, OBJPROP_TEXT, "Close ALL");
        ObjectSetInteger(0, buttonName, OBJPROP_FONTSIZE, 12);
        ObjectSetString(0, buttonName, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, buttonName, OBJPROP_XSIZE, 180);
        ObjectSetInteger(0, buttonName, OBJPROP_YSIZE, 30);
        ObjectSetInteger(0, buttonName, OBJPROP_XDISTANCE, 10);
        ObjectSetInteger(0, buttonName, OBJPROP_YDISTANCE, 300);
        ObjectSetInteger(0, buttonName, OBJPROP_BORDER_TYPE, BORDER_FLAT); // 按钮外观
        ObjectSetInteger(0, buttonName, OBJPROP_BGCOLOR, clrLightGray); // 按钮背景色        
        ObjectSetInteger(0, buttonName, OBJPROP_COLOR, clrBlack); // 文本颜色
    }
    
    if (ObjectFind(0, closeHalfButtonName) == -1) // 如果对象不存在，则创建
    {
        ObjectCreate(0, closeHalfButtonName, OBJ_BUTTON, 0, 0, 0);
        ObjectSetString(0, closeHalfButtonName, OBJPROP_TEXT, "Close Half");
        ObjectSetInteger(0, closeHalfButtonName, OBJPROP_FONTSIZE, 12);
        ObjectSetString(0, closeHalfButtonName, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, closeHalfButtonName, OBJPROP_XSIZE, 180);
        ObjectSetInteger(0, closeHalfButtonName, OBJPROP_YSIZE, 30);
        ObjectSetInteger(0, closeHalfButtonName, OBJPROP_XDISTANCE, 10);
        ObjectSetInteger(0, closeHalfButtonName, OBJPROP_YDISTANCE,350);
        ObjectSetInteger(0, closeHalfButtonName, OBJPROP_BORDER_TYPE, BORDER_FLAT); // 按钮外观
        ObjectSetInteger(0, closeHalfButtonName, OBJPROP_BGCOLOR, clrLightGray); // 按钮背景色        
        ObjectSetInteger(0, closeHalfButtonName, OBJPROP_COLOR, clrBlack); // 文本颜色
    }    
}

//+------------------------------------------------------------------+
//| 处理图表事件                                                    |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if (id == CHARTEVENT_OBJECT_CLICK && sparam == "CloseAllButton")
    {
        CloseAllOrders();
    }
     if (id == CHARTEVENT_OBJECT_CLICK && sparam == closeHalfButtonName)
    {
        CloseHalfOrders();
    }
}
//+------------------------------------------------------------------+
//| 显示界面信息与统计                                               |
//+------------------------------------------------------------------+
void CSSComent() 
{
    // 创建界面元素
    CreateLabel("BG", "g", 180, "Webdings", Purple, 0, 15, false);
    // 显示统计信息
    Coment();
}
//+------------------------------------------------------------------+
//| 创建图表标签对象                                                 |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int fontSize, string font, color colorValue, int xDistance, int yDistance, bool background)
{
    if (ObjectFind(0, name) < 0) 
    {
        ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
        ObjectSetString(0, name, OBJPROP_TEXT, text);
        ObjectSetInteger(0, name, OBJPROP_FONTSIZE, fontSize);
        ObjectSetString(0, name, OBJPROP_FONT, font);
        ObjectSetInteger(0, name, OBJPROP_COLOR, colorValue);
        ObjectSetInteger(0, name, OBJPROP_XDISTANCE, xDistance);
        ObjectSetInteger(0, name, OBJPROP_YDISTANCE, yDistance);
        ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER); // 设置标签的角落位置
        ObjectSetInteger(0, name, OBJPROP_BACK, background ? 1 : 0); // 设置背景属性
    }
}
//+------------------------------------------------------------------+
//| 显示评论窗口信息                                                 |
//+------------------------------------------------------------------+
void Coment() 
{
    // 定义多行文本内容
    string text[];
    ArrayResize(text, 12); // 调整数组大小
    text[0] = StringFormat("%s"," ");
    text[1] = StringFormat("余额：%.2f", accountBalance);
    text[2] = StringFormat("净值：%.2f", accountEquity);
    text[3] = StringFormat("手数：%.2f", lotSize);
    text[4] = StringFormat("小红：%d    低: %.2f", (int)redLineCount, minRedPrice);
    text[5] = StringFormat("小绿：%d    高: %.2f", (int)greenLineCount, maxGreenPrice);
    text[6] = StringFormat("买：%d    买手：%.2f", (int)buyCount,totalBuyVolume);
    text[7] = StringFormat("卖：%d    卖手：%.2f", (int)sellCount,totalSellVolume);
    text[8] = StringFormat("总盈亏：%.2f", currentProfit);
    text[9] = StringFormat("历史盈亏:：%.2f", historyProfit);
    text[10] = StringFormat("起平增量：%d", equityIncrement);
    text[11] = StringFormat("停加：%d    止损：%d", maxLossThreshold,TotalStopLoss);
    CustomComment("CustomText", text, 12, "Arial", clrWhite, 10, 10, 20); // 行间距为20像素
}
void CustomComment(string baseName, string &text[], int fontSize, string font, color colorValue, int xDistance, int yDistance, int lineSpacing)
{
    for (int i = 0; i < ArraySize(text); i++)
    {
        string name = baseName + IntegerToString(i); // 确保每行的对象名称唯一
        if (ObjectFind(0, name) < 0)
        {
            ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
        }
        ObjectSetString(0, name, OBJPROP_TEXT, text[i]);
        ObjectSetInteger(0, name, OBJPROP_FONTSIZE, fontSize);
        ObjectSetString(0, name, OBJPROP_FONT, font);
        ObjectSetInteger(0, name, OBJPROP_COLOR, colorValue);
        ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER); // 左上角
        ObjectSetInteger(0, name, OBJPROP_XDISTANCE, xDistance);
        ObjectSetInteger(0, name, OBJPROP_YDISTANCE, yDistance + i * lineSpacing); // 按行间距调整垂直位置
    }
}

//| 计算总金额                                                  |
//+------------------------------------------------------------------+
void CalculateFunds()
{
    double total = 0.0;
    historyProfit = 0.0;

    // 统计历史订单
    HistorySelect(0, TimeCurrent());
    int totalDeals = HistoryDealsTotal();
    for(int i = 0; i < totalDeals; i++) {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket == 0) continue;
        
        if(HistoryDealGetInteger(ticket, DEAL_ENTRY) != DEAL_ENTRY_OUT) 
            continue;

        if(HistoryDealGetInteger(ticket, DEAL_MAGIC) != MagicNumber ||
           HistoryDealGetString(ticket, DEAL_SYMBOL) != _Symbol)
            continue;

        double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT) + 
                        HistoryDealGetDouble(ticket, DEAL_COMMISSION) + 
                        HistoryDealGetDouble(ticket, DEAL_SWAP);
                        
        total += profit;
        historyProfit += profit;
    }

    // 统计持仓订单
    int totalPositions = PositionsTotal();
    for(int i = 0; i < totalPositions; i++) {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket)) {
            if(PositionGetInteger(POSITION_MAGIC) == MagicNumber && 
               PositionGetString(POSITION_SYMBOL) == _Symbol) {
                double profit = PositionGetDouble(POSITION_PROFIT) + 
                               PositionGetDouble(POSITION_SWAP) + 
                               PositionGetDouble(POSITION_COMMISSION);
                total += profit;
            }
        }
    }
    currentProfit = total;
}

void CloseProfitableOrders(double targetProfit) {
    double profits[];
    ulong tickets[];
    int count = 0;
    
    // 获取当前持仓订单收益
    int totalPositions = PositionsTotal();
    for (int i = 0; i < totalPositions; i++) {
        ulong ticket = PositionGetTicket(i);
        if (PositionSelectByTicket(ticket)) {
            if (PositionGetString(POSITION_SYMBOL) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
                double profit = PositionGetDouble(POSITION_PROFIT) +
                                PositionGetDouble(POSITION_COMMISSION) +
                                PositionGetDouble(POSITION_SWAP);
                ArrayResize(profits, count + 1);
                ArrayResize(tickets, count + 1);
                profits[count] = profit;
                tickets[count] = ticket;
                count++;
            }
        }
    }
    

    // 如果总收益大于指定金额，进行平仓操作
    if (currentProfit > targetProfit) {
        // 按照收益从低到高排序，仅对当前持仓订单进行排序
        for (int i = 0; i < count - 1; i++) {
            for (int j = i + 1; j < count; j++) {
                if (profits[i] > profits[j]) {
                    double tempProfit = profits[i];
                    ulong tempTicket = tickets[i];
                    profits[i] = profits[j];
                    tickets[i] = tickets[j];
                    profits[j] = tempProfit;
                    tickets[j] = tempTicket;
                }
            }
        }
        
        CTrade m_trade;             // 交易对象
        CPositionInfo m_position;   // 持仓信息对象
        CArrayLong m_arr_tickets;   // 存储订单号的数组
        m_trade.SetDeviationInPoints(INT_MAX);  // 设置最大滑点
        m_trade.SetAsyncMode(true);              // 启用异步模式
        m_trade.SetMarginMode();                 // 设置保证金模式
        m_trade.LogLevel(LOG_LEVEL_ERRORS);     // 设置日志级别
        bool result = true;
        m_arr_tickets.Shutdown();  // 清空存储订单号的数组
      
        // 计算需要平仓的订单数量，使总收益达到净值增量/2
        double mytargetProfit = targetProfit / 2 + MathAbs(historyProfit);
        double accumulatedProfit = 0;
        int limit = 0;
        
        // 计算需要平仓的订单数量
        for (int i = 0; i < count; i++) {
            accumulatedProfit += profits[i];
            limit++;
            if (accumulatedProfit >= mytargetProfit) break;
        }

       // Close positions matching the target Magic Number and Symbol
       for (int i = 0; i < limit; i++)
       {
           if (!m_position.SelectByTicket(tickets[i]))
           {
               PrintFormat("> Error: selecting position with index #%d failed. Error Code: %d", i, GetLastError());
               result = false;
               continue;
           }
   
            // Add position ticket to the array
            if (!m_arr_tickets.Add(m_position.Ticket()))
            {
                PrintFormat("> Error: adding position ticket #%I64u failed.", m_position.Ticket());
                result = false;
                continue;
            }
       }


       for (int i = 0; i < m_arr_tickets.Total(); i++) {
           ulong ticket = m_arr_tickets.At(i);
           if (!m_position.SelectByTicket(ticket)) {
               PrintFormat("> Error: selecting position ticket #%I64u failed. Error Code: %d", ticket, GetLastError());
               result = false;
               continue;
           }
   
           m_trade.SetExpertMagicNumber(m_position.Magic());  // 设置魔术号
           m_trade.SetTypeFillingBySymbol(m_position.Symbol());  // 设置填充模式
  
   
           // 发起平仓请求
           if (m_trade.PositionClose(ticket) && (m_trade.ResultRetcode() == TRADE_RETCODE_DONE || m_trade.ResultRetcode() == TRADE_RETCODE_PLACED)) {
               PrintFormat("Position ticket #%I64u on %s closed successfully.", m_position.Ticket(), m_position.Symbol());
               PlaySound("expert.wav");
           } else {
               PrintFormat("> Error: closing position ticket #%I64u on %s failed. Retcode=%u (%s)", m_position.Ticket(), m_position.Symbol(), m_trade.ResultRetcode(), m_trade.ResultComment());
               result = false;
           }
       }
       
       // 如果所有操作成功，播放完成音效，否则播放失败音效
       if (result) {
           PlaySound("done.wav");
           Print("总盈亏：",currentProfit,"   历史盈亏:",historyProfit);
           ExpertRemove();  // 移除当前的EA
       } else {
           PlaySound("timeout.wav");
           Print("Some operations failed. Please check the logs for errors.");
       }       
             
    } else {
        //Print("总收益未达到指定金额，无需平仓。");
    }
}


void CloseHalfOrders() {
    double profits[];
    ulong tickets[];
    int count = 0;
    
    // 获取当前持仓订单收益
    int totalPositions = PositionsTotal();
    for (int i = 0; i < totalPositions; i++) {
        ulong ticket = PositionGetTicket(i);
        if (PositionSelectByTicket(ticket)) {
            if (PositionGetString(POSITION_SYMBOL) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
                double profit = PositionGetDouble(POSITION_PROFIT) +
                                PositionGetDouble(POSITION_COMMISSION) +
                                PositionGetDouble(POSITION_SWAP);
                ArrayResize(profits, count + 1);
                ArrayResize(tickets, count + 1);
                profits[count] = profit;
                tickets[count] = ticket;
                count++;
            }
        }
    }
    


    if (count > 0) {
        // 按照收益从低到高排序，仅对当前持仓订单进行排序
        for (int i = 0; i < count - 1; i++) {
            for (int j = i + 1; j < count; j++) {
                if (profits[i] > profits[j]) {
                    double tempProfit = profits[i];
                    ulong tempTicket = tickets[i];
                    profits[i] = profits[j];
                    tickets[i] = tickets[j];
                    profits[j] = tempProfit;
                    tickets[j] = tempTicket;
                }
            }
        }
        
        CTrade m_trade;             // 交易对象
        CPositionInfo m_position;   // 持仓信息对象
        CArrayLong m_arr_tickets;   // 存储订单号的数组
        m_trade.SetDeviationInPoints(INT_MAX);  // 设置最大滑点
        m_trade.SetAsyncMode(true);              // 启用异步模式
        m_trade.SetMarginMode();                 // 设置保证金模式
        m_trade.LogLevel(LOG_LEVEL_ERRORS);     // 设置日志级别
        bool result = true;
        m_arr_tickets.Shutdown();  // 清空存储订单号的数组
      


       // Close positions matching the target Magic Number and Symbol
       for (int i = 0; i < count/2; i++)
       {
           if (!m_position.SelectByTicket(tickets[i]))
           {
               PrintFormat("> Error: selecting position with index #%d failed. Error Code: %d", i, GetLastError());
               result = false;
               continue;
           }
   
            // Add position ticket to the array
            if (!m_arr_tickets.Add(m_position.Ticket()))
            {
                PrintFormat("> Error: adding position ticket #%I64u failed.", m_position.Ticket());
                result = false;
                continue;
            }
       }


       for (int i = 0; i < m_arr_tickets.Total(); i++) {
           ulong ticket = m_arr_tickets.At(i);
           if (!m_position.SelectByTicket(ticket)) {
               PrintFormat("> Error: selecting position ticket #%I64u failed. Error Code: %d", ticket, GetLastError());
               result = false;
               continue;
           }
   
           m_trade.SetExpertMagicNumber(m_position.Magic());  // 设置魔术号
           m_trade.SetTypeFillingBySymbol(m_position.Symbol());  // 设置填充模式
  
   
           // 发起平仓请求
           if (m_trade.PositionClose(ticket) && (m_trade.ResultRetcode() == TRADE_RETCODE_DONE || m_trade.ResultRetcode() == TRADE_RETCODE_PLACED)) {
               PrintFormat("Position ticket #%I64u on %s closed successfully.", m_position.Ticket(), m_position.Symbol());
               PlaySound("expert.wav");
           } else {
               PrintFormat("> Error: closing position ticket #%I64u on %s failed. Retcode=%u (%s)", m_position.Ticket(), m_position.Symbol(), m_trade.ResultRetcode(), m_trade.ResultComment());
               result = false;
           }
       }
       
       // 如果所有操作成功，播放完成音效，否则播放失败音效
       if (result) {
           PlaySound("done.wav");
           Print("平仓一半");
       } else {
           PlaySound("timeout.wav");
           Print("Some operations failed. Please check the logs for errors.");
       }       
             
    } else {
        //Print("总收益未达到指定金额，无需平仓。");
    }
}
//+------------------------------------------------------------------+