-- 修改 VideoList 表中的 varchar 字段为 nvarchar 字段
-- 执行前请确保备份数据库
-- 此脚本会处理可能存在的索引问题

USE [edsynwx]
GO

-- 检查现有索引
PRINT '检查 VideoList 表的现有索引...'
SELECT 
    i.name AS IndexName,
    c.name AS ColumnName,
    i.type_desc AS IndexType
FROM sys.indexes i
INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE i.object_id = OBJECT_ID('dbo.VideoList')
    AND c.name IN ('polyvVid', 'cateId', 'cover')
ORDER BY i.name, ic.key_ordinal;
GO

-- 声明变量存储索引信息
DECLARE @sql NVARCHAR(MAX) = ''
DECLARE @indexName NVARCHAR(128)
DECLARE @columnName NVARCHAR(128)
DECLARE @indexType NVARCHAR(60)

-- 创建临时表存储需要重建的索引
IF OBJECT_ID('tempdb..#IndexesToRecreate') IS NOT NULL
    DROP TABLE #IndexesToRecreate

CREATE TABLE #IndexesToRecreate (
    IndexName NVARCHAR(128),
    ColumnName NVARCHAR(128),
    IndexType NVARCHAR(60),
    IndexDefinition NVARCHAR(MAX)
)

-- 获取涉及 varchar 字段的索引信息
INSERT INTO #IndexesToRecreate (IndexName, ColumnName, IndexType)
SELECT DISTINCT
    i.name,
    c.name,
    i.type_desc
FROM sys.indexes i
INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE i.object_id = OBJECT_ID('dbo.VideoList')
    AND c.name IN ('polyvVid', 'cateId', 'cover')
    AND i.name IS NOT NULL  -- 排除主键聚集索引

-- 如果存在索引，先删除它们
IF EXISTS (SELECT 1 FROM #IndexesToRecreate)
BEGIN
    PRINT '发现涉及 varchar 字段的索引，正在删除...'
    
    DECLARE index_cursor CURSOR FOR
    SELECT DISTINCT IndexName FROM #IndexesToRecreate
    
    OPEN index_cursor
    FETCH NEXT FROM index_cursor INTO @indexName
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        SET @sql = 'DROP INDEX [' + @indexName + '] ON [dbo].[VideoList]'
        PRINT '执行: ' + @sql
        EXEC sp_executesql @sql
        
        FETCH NEXT FROM index_cursor INTO @indexName
    END
    
    CLOSE index_cursor
    DEALLOCATE index_cursor
END

-- 修改字段类型
PRINT '正在修改字段类型...'

-- 修改 polyvVid 字段从 varchar(50) 到 nvarchar(50)
ALTER TABLE [dbo].[VideoList]
ALTER COLUMN [polyvVid] [nvarchar](50) NOT NULL;
PRINT 'polyvVid 字段已修改为 nvarchar(50)'
GO

-- 修改 cateId 字段从 varchar(50) 到 nvarchar(50)
ALTER TABLE [dbo].[VideoList]
ALTER COLUMN [cateId] [nvarchar](50) NOT NULL;
PRINT 'cateId 字段已修改为 nvarchar(50)'
GO

-- 修改 cover 字段从 varchar(255) 到 nvarchar(255)
ALTER TABLE [dbo].[VideoList]
ALTER COLUMN [cover] [nvarchar](255) NOT NULL;
PRINT 'cover 字段已修改为 nvarchar(255)'
GO

-- 重新创建索引（如果之前存在的话）
-- 注意：这里需要根据实际的索引定义来重建
-- 以下是常见索引的示例，请根据实际情况调整

-- 如果 polyvVid 有唯一索引
-- CREATE UNIQUE NONCLUSTERED INDEX [IX_VideoList_polyvVid] ON [dbo].[VideoList]([polyvVid] ASC)

-- 如果 cateId 有普通索引
-- CREATE NONCLUSTERED INDEX [IX_VideoList_cateId] ON [dbo].[VideoList]([cateId] ASC)

-- 验证修改结果
PRINT '验证修改结果...'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'VideoList' 
    AND COLUMN_NAME IN ('polyvVid', 'cateId', 'cover')
ORDER BY COLUMN_NAME;

-- 清理临时表
IF OBJECT_ID('tempdb..#IndexesToRecreate') IS NOT NULL
    DROP TABLE #IndexesToRecreate

PRINT '字段类型修改完成！'
GO
