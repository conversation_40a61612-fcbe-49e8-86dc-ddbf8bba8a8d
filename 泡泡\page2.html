<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
<title>奥秘</title>
<style>
html, body {
    border: none;
    overflow: hidden;
    width: 100vw;
	height: 100vh; 
    margin: 0;
    padding: 0;
}

body { 
    background: url(BG.jpg) bottom; 
    position: relative;
    display: flex;
    flex-direction: column; /* 改为垂直排列 */
}
.video-container {
    display: flex;
    width: 100vw;
    max-width: 100vw;
    height: 100vh;
    justify-content: center;
    align-items: center;
    margin: 0;
    background: none;
}
.video-list {
    width: 15vw; 
    background: rgba(20, 20, 20, 0.8);
    padding: 15px;
    overflow-y: auto;
    height: 100%;  /* 新增高度设置 */
}

.video-list h3 {
    color: #fff;
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #444;
}

.video-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.video-list li {
    padding: 1.5vh 1vw;
    margin: 1vh 0;
	font-size: 2vh; 
    background: rgba(50, 50, 50, 0.5);
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.video-list li:hover {
    background: rgba(70, 70, 70, 0.7);
}

.video-list li.active {
    background: rgba(100, 100, 100, 0.9);
    font-weight: bold;
}

.video-player {
    flex: 1 1 auto;  /* 修改flex属性为自动填充 */
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
    width: auto;  /* 显式声明宽度自动 */
}

.video-container {
    display: flex;
    width: 100%;  /* 改为容器宽度100% */
    height: 100%; /* 改为容器高度100% */
    margin: 0;
    background: none;
}

.video-controls button {
    padding: 1vh 2vw;
	font-size: 2vh;
    background: rgba(70, 70, 70, 0.7);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin: 0; /* 移除默认外边距 */
}

.video-controls button:hover {
    background: rgba(100, 100, 100, 0.9);
}

#volumeControl {
    width: 80px;
}

#videoTitle {
    color: #fff;
    margin-bottom: 2vh;
    text-align: center;
	font-size: 3vh;
	display: none; 
}

/* 5 个水平排列的区域 */
.region {
    height: 100%; /* 区域占满整个高度 */
	min-height: 100vh;
	width: 100%;  /* 区域占满整个宽度 */
	position: relative;
	background: rgba(0, 0, 0, 0.2); /* 设置背景色 */
}
/* 利用伪元素在每个分区底部添加标注，类似CAD测量距离的风格 */
.region::after {
    content: attr(data-label);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.85);
    background: rgba(0, 0, 0, 0.5);
    padding: 4px 0;
    border-top: 1px dashed rgba(255, 255, 255, 0.7);
}


/* 美化弹出窗口 */
.popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    width: 60vw;  /* 修改宽度为视口的90% */
    height: 90vh; /* 修改高度为视口的90% */
    transform: translate(-50%, -50%); /* 居中定位 */
    background: #000;
    color: #fff;
    border: none;
    padding: 0;
    z-index: 1000;
    overflow: hidden; /* 改为hidden避免滚动条 */
    box-shadow: 0 0 20px rgba(0,0,0,0.5);
    border-radius: 10px;
}

.popup h2 {
    margin-top: 0;
	margin: 2vh 0;
    color: #fff; /* 标题文字改为白色 */
	font-size: 3.5vh; 
	text-shadow: 0 0 10px rgba(255,255,255,0.3);
}

.popup p {
    font-size: 2.5vh; 
    line-height: 1.6;
    color: #fff; /* 正文文字改为白色 */
    text-align: left;
    margin: 15px 0;
}
.popup video {
    width: 100%;  /* 改为100%填充容器 */
    height: 100%;
    object-fit: contain;
    margin: 0;
    display: block;
    background: #000;
    border: 1px solid #444;
}
#bubbleId {
    display: none; /* 隐藏泡泡ID显示 */
}

/* 返回首页按钮样式，固定在页面右下角 */
.back-btn {
    position: fixed;
    right: 20px;
    bottom: 35px;
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    font-size: 50px; /* 从16px增大到20px */
    font-weight: bold;
    padding: 15px 25px; /* 从10px 20px增大到15px 25px */
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    text-decoration: none;
    transition: background 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
    z-index: 1001;
    min-width: 120px; /* 添加最小宽度限制 */
}
.back-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
}
</style>
</head>

<body onload="Demo()">
    <!-- 5 个水平排列的区域，设置 data-label 属性标注分区名称 -->
    <div class="region" data-label="奥秘"></div>

    <!-- 弹出窗口，内容为图文信息，文字可能较长 -->
    <div class="popup" id="bubblePopup">
        <div class="video-container">
            <div class="video-list">
                <h3>视频清单</h3>
                <ul id="videoPlaylist"></ul>
            </div>
            <div class="video-player">
                <h2 id="videoTitle"></h2>
                <video id="bubbleVideo" autoplay playsinline>
                    <source src="" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
                <div class="video-controls">
                    <button id="playPauseBtn">暂停</button>
                    <button id="back15Btn">-15秒</button>
                    <button id="forward15Btn">+15秒</button>
                    <input type="range" id="volumeControl" min="0" max="1" step="0.1" value="1">
                    <button id="closeButton">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 返回首页按钮，固定在右下角 -->
    <button id="homeButton" class="back-btn">HOME</button>

<script>
// 添加泡泡标题数组
var bubbleTitles = [
"诞生之谜",
"终结之谜",
"规律之谜",
"对称之谜",
"时间之谜",
"旅行之谜",
"光速之谜",
"量子之谜",
"意识之谜",
"起源之谜",
"智慧之谜",
"灵魂之谜",
"数学之谜",
"神灵之谜",
"上帝之谜",
"真理之谜",
"思维缺陷",
"蝴蝶效应",
"混乱地球",
"拯救露露",
"两种定义",
"因果分离",
"一时多空",
"一空多时",
"永恒宇宙",
"罗素悖论",
"宇宙概念",
"宇宙定义",
"静元宇宙",
"单元宇宙",
"观察宇宙",
"万维宇宙",
"神奇特征",
"有无合一",
"对错合一",
"悖论本元",
"颜色相对",
"名称相对",
"大小相对",
"快慢相对",
"能力相对",
"变动相对",
"需求相对",
"利益相对",
"数学相对",
"物理相对",
"迷信相对",
"神话相对",
"语言相对",
"传递相对",
"爱情相对",
"管理相对1",
"管理相对2",
"缘起成相",
"客体无相",
"主体无相",
"客体无定",
"岳飞之死",
"主体无定",
"因果悖论",
"自然对称",
"人体对称",
"文学对称",
"建筑对称",
"情感对称",
"数学对称",
"物理对称",
"规律对称",
"手性不对称",
"宇称不对称",
"粒子不对称",
"时间不对称",
"万相不对称",
"对称本元1",
"对称本元2",
"美的本元",
"因果对称1",
"因果对称2",
"运动循环",
"人类循环1",
"人类循环2",
"人类循环3",
"万相循环",
"物极必反",
"亢龙有悔",
"永无止尽",
"飞龙在天",
"循环本元",
"公转之谜",
"多重循环",
"大小本元",
"变化悖论",
"差别不同1",
"差别不同2",
"变动不同",
"认知不同",
"万相不同",
"真理不同",
"心物互动",
"奇点理论",
"天行无常",
"变动有常",
"博弈法则1",
"博弈法则2",
"例外原则1",
"例外原则2",
"命运无常",
"命运无常",
"命运选择",
"差别模糊",
"变动模糊",
"认知模糊",
"万相模糊",
"真理模糊",
"意识模糊",
"时间模糊",
"空间模糊",
"我是谁",
"我在何处",
"知我我知",
"生命定义",
"黏菌社交",
"自由意识",
"万物都是生命",
"石头的自由意识",
"极端生命",
"万相都是生命",
"生命起源于生命1",
"生命起源于生命2",
"生命存在生命中",
"生命组成了生命",
"生命本元",
"生命是意识融合",
"生命是加权融合1",
"生命是加权融合2",
"意识独立于意识",
"意识映射意识",
"同存奇迹",
"共生奇迹1",
"共生奇迹2",
"模糊奇迹",
"智能意识",
"毁灭预言",
"神话时代",
"芝诺悖论",
"芝诺悖论的多解1",
"芝诺悖论的多解2",
"芝诺悖论的分析1",
"芝诺悖论的分析2",
"连续和间断",
"无限和有限",
"数学本元",
"宇宙数学",
"量子时空",
"量子速度",
"量子意识",
"量子世界",
"芝诺悖论解答",
"生死悖论",
"第一推动力",
"真空和妙有",
"波函数",
"宇宙暴长现象",
"万物由来",
"万物演变",
"同生同死",
"方生方死",
"缘生缘死",
"生死之谜",
"维度之谜",
"光速之谜",
"绝对时空",
"相对论假设",
"相对论效应",
"孪生子佯谬1",
"孪生子佯谬2",
"狭义相对论漏洞",
"光速可变",
"铁笼原理",
"漂移原理",
"漂移速度",
"漂移效应",
"孪生子佯谬解答",
"时间本元",
"光速来源",
"漂移预测",
"波动悖论",
"波粒之争1",
"波粒之争2",
"波粒二重性",
"幽灵困惑1",
"幽灵困惑2",
"时空波动",
"观察波动",
"异日重现",
"幽灵之谜",
"猫论之谜解答",
"衍射之谜解答",
"神灵悖论",
"时空神话",
"分身神通",
"他心神通",
"上帝存在论证",
"上帝能力悖论",
"仁慈悖论",
"上帝出生悖论",
"佛的本相",
"佛法本相",
"佛经方法",
"宗教缘起本元",
"一神多神",
"崇善合一",
];
// 定义每个区域目标泡泡数及当前计数
var bubbleTarget = [bubbleTitles.length]; 
var bubbleCount = [0];
var bubbleIdCounter = 1; // 唯一ID计数器
var currentRegionIndex = 0; // 当前生成泡泡的区域索引

function Demo() {
    if (currentRegionIndex < bubbleTarget.length) {
        if (bubbleCount[currentRegionIndex] < bubbleTarget[currentRegionIndex]) {
            CreateBubble(currentRegionIndex);
            bubbleCount[currentRegionIndex]++;
        }
        if (bubbleCount[currentRegionIndex] < bubbleTarget[currentRegionIndex]) {
            setTimeout(Demo, 0);
        } else {
            currentRegionIndex++;
            setTimeout(Demo, 0);
        }
    }
}

+function() {
    var _VER_ = navigator.userAgent;
    var _IE6_ = /IE 6/.test(_VER_);
    var STD = !!window.addEventListener;
    var de = document.documentElement;

    _IE6_ && document.execCommand("BackgroundImageCache", false, true);

    var K = 0.999;
    var POW_RATE = 0.0001;
    var POW_RANGE = 0.8;

    function SPEED_X() { return 4 + RND() * 4; }
    function SPEED_Y() { return 3 + RND() * 2; }

    var arrBubs = [];
    var iBottom;
    var iRight;

    var SQRT = Math.sqrt;
    var ATAN2 = Math.atan2;
    var SIN = Math.sin;
    var COS = Math.cos;
    var ABS = Math.abs;
    var RND = Math.random;
    var ROUND = Math.round;

    function Timer(call, time) {
        var last = +new Date;
        var delay = 0;
        return setInterval(function() {
            var cur = +new Date;
            delay += (cur - last);
            last = cur;
            if (delay >= time) {
                call();
                delay %= time;
            }
        }, 1);
    }

    Timer(update, 17);

    function CreateBubble(regionIndex) {
        var region = document.querySelector(".region");
        var regionRect = region.getBoundingClientRect();

        var bub = new Bubble(100, bubbleIdCounter++);
        var startX = regionRect.left + Math.random() * (regionRect.width - 10);
        var startY = regionRect.top + Math.random() * (regionRect.height - 10);

        bub.setX(startX);
        bub.setY(startY);
        bub.vx = SPEED_X() * (Math.random() > 0.5 ? 1 : -1);
        bub.vy = SPEED_Y() * (Math.random() > 0.5 ? 1 : -1);
        bub.regionBounds = regionRect;

        arrBubs.push(bub);
        bub.oBox.onclick = function() { showPopup(bub.id); };
    }

    window.CreateBubble = CreateBubble;
    
    function update() {
        var n = arrBubs.length;
        var bub, bub2;

        updateWall();

        for (var i = 0; i < n; i++) {
            bub = arrBubs[i];
            bub.paint();
            bub.vx *= K;
            bub.vy *= K;

            if (RND() < 0.01) {
                var newTargetD = 100 + RND() * 150;  // 增大泡泡尺寸范围(80-280px)
                bub.setTargetSize(newTargetD);
            }

            if (bub.D !== bub.targetD) {
                var delta = bub.targetD - bub.D;
                if (Math.abs(delta) < bub.sizeSpeed) {
                    bub.D = bub.targetD;
                } else {
                    bub.D += delta > 0 ? bub.sizeSpeed : -bub.sizeSpeed;
                }
                bub.updateSize();
            }

            if (RND() < POW_RATE) {
                bub.vx = SPEED_X() * (1 + RND() * POW_RANGE);
                bub.vy = SPEED_Y() * (1 + RND() * POW_RANGE);
            }

            bub.setX(bub.x + bub.vx);
            bub.setY(bub.y + bub.vy);
            checkWalls(bub);
        }

        for (var i = 0; i < n - 1; i++) {
            bub = arrBubs[i];
            for (var j = i + 1; j < n; j++) {
                bub2 = arrBubs[j];
                checkCollision(bub, bub2);
            }
        }
    }

    function updateWall() {
        iRight = de.clientWidth;
        iBottom = de.clientHeight;
    }

    function checkWalls(bub) {
        var bounds = bub.regionBounds;

        if (bub.x < bounds.left) {
            bub.setX(bounds.left);
            bub.vx *= -1;
        } else if (bub.x > bounds.right - bub.D) {
            bub.setX(bounds.right - bub.D);
            bub.vx *= -1;
        }

        if (bub.y < bounds.top) {
            bub.setY(bounds.top);
            bub.vy *= -1;
        } else if (bub.y > bounds.bottom - bub.D) {
            bub.setY(bounds.bottom - bub.D);
            bub.vy *= -1;
        }
    }

    function rotate(x, y, sin, cos, reverse) {
        if (reverse)
            return { x: x * cos + y * sin, y: y * cos - x * sin };
        else
            return { x: x * cos - y * sin, y: y * cos + x * sin };
    }

    function checkCollision(bub0, bub1) {
        var dx = bub1.x - bub0.x;
        var dy = bub1.y - bub0.y;
        var dist = SQRT(dx * dx + dy * dy);
        var minDist = (bub0.D + bub1.D) / 2;

        if (dist < minDist) {
            var angle = ATAN2(dy, dx);
            var sin = SIN(angle);
            var cos = COS(angle);

            var pos0 = { x: 0, y: 0 };
            var pos1 = rotate(dx, dy, sin, cos, true);

            var vel0 = rotate(bub0.vx, bub0.vy, sin, cos, true);
            var vel1 = rotate(bub1.vx, bub1.vy, sin, cos, true);

            var vxTotal = vel0.x - vel1.x;
            vel0.x = vel1.x;
            vel1.x = vxTotal + vel0.x;

            var absV = ABS(vel0.x) + ABS(vel1.x);
            var overlap = minDist - ABS(pos0.x - pos1.x);

            pos0.x += vel0.x / absV * overlap;
            pos1.x += vel1.x / absV * overlap;

            var pos0F = rotate(pos0.x, pos0.y, sin, cos, false);
            var pos1F = rotate(pos1.x, pos1.y, sin, cos, false);

            bub1.setX(bub0.x + pos1F.x);
            bub1.setY(bub0.y + pos1F.y);
            bub0.setX(bub0.x + pos0F.x);
            bub0.setY(bub0.y + pos0F.y);

            var vel0F = rotate(vel0.x, vel0.y, sin, cos, false);
            var vel1F = rotate(vel1.x, vel1.y, sin, cos, false);

            bub0.vx = vel0F.x;
            bub0.vy = vel0F.y;
            bub1.vx = vel1F.x;
            bub1.vy = vel1F.y;
        }
    }

    var APLHA = 0.8;
    var POW = [1, APLHA, APLHA * APLHA];

    function Bubble(initialD, id) {
        this.D = initialD;
        this.targetD = initialD;
        this.sizeSpeed = 2;
        this.id = id;
        var kOpa = [], kStp = [];
        var arrFlt = [];
        var oBox = document.body.appendChild(document.createElement("div"));
        
        // 添加文字显示
        var textDiv = document.createElement("div");
        textDiv.style.position = "absolute";
        textDiv.style.width = "100%";
        textDiv.style.height = "100%";
        textDiv.style.display = "flex";
        textDiv.style.flexDirection = "column"; // 改为垂直排列
        textDiv.style.alignItems = "center";
        textDiv.style.justifyContent = "center";
        textDiv.style.color = "#fff";
        textDiv.style.fontWeight = "bold";
        textDiv.style.textShadow = "1px 1px 2px #000";
        textDiv.style.fontSize = Math.max(1, this.D / 5) + "px";
        textDiv.style.lineHeight = "1.2"; // 调整行高
        textDiv.style.textAlign = "center"; // 文字居中
        textDiv.style.wordBreak = "break-all"; // 允许换行
        
        // 截取最多8个字符(4字×2行)
        var title = bubbleTitles[id - 1];
        var displayText = "";
        for (var i = 0; i < Math.min(8, title.length); i++) {
        	displayText += title[i];
        	if ((i + 1) % 4 === 0 && i !== 7) { // 每4个字换行
        		displayText += "\n";
        	}
        }
        textDiv.innerText = displayText;
        oBox.appendChild(textDiv);

        styBox = oBox.style;
        styBox.position = "absolute";
        styBox.width = this.D + "px";
        styBox.height = this.D + "px";        

        for (var i = 0; i < 4; i++) {
            var div = document.createElement("div");
            var sty = div.style;
            sty.position = "absolute";
            sty.width = this.D + "px";
            sty.height = this.D + "px";
            oBox.appendChild(div);
            if (i == 3) {
                if (_IE6_)
                    sty.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(src=heart.png)";
                else
                    sty.backgroundImage = "url(heart.png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                break;
            }
            kOpa[i] = 3 * RND();
            kStp[i] = 0.02 * RND();
            if (STD) {
                sty.backgroundImage = "url(ch" + i + ".png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                arrFlt[i] = sty;
            } else {
                sty.filter = "alpha progid:DXImageTransform.Microsoft.AlphaImageLoader(src=ch" + i + ".png)";
                arrFlt[i] = div.filters.alpha;
            }
        }

        this.styBox = styBox;
        this.oBox = oBox;
        this.kOpa = kOpa;
        this.kStp = kStp;
        this.arrFlt = arrFlt;
        this.updateSize = function() {
        this.styBox.width = this.D + "px";
        this.styBox.height = this.D + "px";
        // 文字大小调整为泡泡直径的1/4，确保不超出泡泡范围
        textDiv.style.fontSize = Math.max(1, this.D / 6) + "px";
        // 设置文字容器最大宽度为泡泡直径的90%
        textDiv.style.maxWidth = this.D * 0.9 + "px";
        // 设置文字不换行
        textDiv.style.whiteSpace = "nowrap";
        // 超出部分显示省略号
        textDiv.style.overflow = "hidden";
        textDiv.style.textOverflow = "ellipsis";
        
        for (var i = 0; i < this.oBox.children.length; i++) {
            var div = this.oBox.children[i];
            div.style.width = this.D + "px";
            div.style.height = this.D + "px";
            div.style.backgroundSize = this.D + "px " + this.D + "px";
        }
        };
		// 添加触摸事件支持
		oBox.addEventListener('touchstart', function(e) {
			e.preventDefault();
			this.style.transform = 'scale(0.95)';
		}, {passive: false});
		
		oBox.addEventListener('touchend', function(e) {
			e.preventDefault();
			this.style.transform = '';
			// 触发点击事件
			if (!document.querySelector('.touch-active')) {
				this.click();
			}
		}, {passive: false});
		
		oBox.addEventListener('touchcancel', function() {
			this.style.transform = '';
		});
    }

    Bubble.prototype.setX = function(x) {
        this.x = x;
        this.styBox.left = ROUND(x) + "px";
    };

    Bubble.prototype.setY = function(y) {
        this.y = y;
        this.styBox.top = ROUND(y) + "px";
    };

    Bubble.prototype.setTargetSize = function(newTargetD) {
        this.targetD = newTargetD;
    };

    Bubble.prototype.updateSize = function() {
        this.styBox.width = this.D + "px";
        this.styBox.height = this.D + "px";
        for (var i = 0; i < this.oBox.children.length; i++) {
            var div = this.oBox.children[i];
            div.style.width = this.D + "px";
            div.style.height = this.D + "px";
            div.style.backgroundSize = this.D + "px " + this.D + "px";
        }
    };

    Bubble.prototype.paint = function() {
        var i, v;
        for (i = 0; i < 3; i++) {
            v = ABS(SIN(this.kOpa[i] += this.kStp[i] * RND()));
            v *= POW[i];
            v = ((v * 1e4) >> 0) / 1e4;
            this.arrFlt[i].opacity = STD ? v : v * 100;
        }
    };

	function showPopup(bubbleId) {
	    var popup = document.getElementById('bubblePopup');
	    popup.style.display = 'block';
	    
	    // 填充视频清单
	    var playlist = document.getElementById('videoPlaylist');
	    playlist.innerHTML = '';
	    bubbleTitles.forEach(function(title, index) {
	        var li = document.createElement('li');
	        li.textContent = title;
	        if (index + 1 === bubbleId) {
	            li.classList.add('active');
	        }
	        li.onclick = function() {
	            playVideo(index + 1);
	        };
	        playlist.appendChild(li);
	    });
	    
	    playVideo(bubbleId);
	}
	function handleVideoEnd() {
	    var items = document.querySelectorAll('#videoPlaylist li');
	    var currentLi = document.querySelector('#videoPlaylist li.active');
	    if (currentLi) {
	        var nextLi = currentLi.nextElementSibling || items[0]; // 循环到第一个
	        nextLi.click();
	    }
	}
	
	function playVideo(bubbleId) {
		    var title = bubbleTitles[bubbleId - 1];
		    document.getElementById('videoTitle').innerText = title;
		    
		    // 更新激活状态
		    var items = document.querySelectorAll('#videoPlaylist li');
		    items.forEach(function(item, index) {
		        if (index + 1 === bubbleId) {
		            item.classList.add('active');
		        } else {
		            item.classList.remove('active');
		        }
		    });
		    
		    // 播放视频
		    var video = document.getElementById('bubbleVideo');
			// 移除旧的结束监听
			video.removeEventListener('ended', handleVideoEnd);
			// 添加新的结束监听
			video.addEventListener('ended', handleVideoEnd);
		    video.src = "static/mystery/" + bubbleId + ".mp4";
		    video.currentTime = 0;
			// 添加控制按钮事件
			var playPauseBtn = document.getElementById('playPauseBtn');
			var back15Btn = document.getElementById('back15Btn');
			var forward15Btn = document.getElementById('forward15Btn');
			var volumeControl = document.getElementById('volumeControl');
			
			playPauseBtn.addEventListener('click', function() {
				if (video.paused) {
					video.play();
					playPauseBtn.textContent = '暂停';
				} else {
					video.pause();
					playPauseBtn.textContent = '播放';
				}
			});
			
			back15Btn.addEventListener('click', function() {
				video.currentTime = Math.max(0, video.currentTime - 15);
			});
			
			forward15Btn.addEventListener('click', function() {
				video.currentTime = Math.min(video.duration, video.currentTime + 15);
			});
			
			volumeControl.addEventListener('input', function() {
				video.volume = this.value;
			});
			    
			video.load();
		    video.play().catch(e => console.log("自动播放被阻止:", e));
			// 添加视频点击事件
			    video.onclick = function() {
			        if (video.paused) {
			            video.play();
			            document.getElementById('playPauseBtn').textContent = '暂停';
			        } else {
			            video.pause();
			            document.getElementById('playPauseBtn').textContent = '播放';
			        }
			    };
		}

    function closePopup() {
		var video = document.getElementById('bubbleVideo');
		video.pause(); // 停止视频播放
		video.currentTime = 0; // 重置播放进度
        document.getElementById('bubblePopup').style.display = 'none';
    }

    document.getElementById('closeButton').onclick = closePopup;

}();

document.getElementById('homeButton').addEventListener('click', function() {
    window.location.href = 'index.html';
});
// 为返回按钮添加触摸支持
document.getElementById('homeButton').addEventListener('touchstart', function(e) {
    e.preventDefault();
    this.click();
}, {passive: false});
</script>
</body>
</html>
