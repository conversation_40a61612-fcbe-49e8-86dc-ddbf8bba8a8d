<template>
	<view class="container">
	  <view class="header-title">
		爱迪生科学小站
	  </view> 
	  <swiper
		class="swiper-container"
		:autoplay="true"
		:circular="true"
		:indicator-dots="true"
		indicator-active-color="#007AFF"
		indicator-color="#CCC"
		:interval="3000"
		:duration="500">
		<swiper-item v-for="(item, index) in swiperlist1" :key="index">
		  <view class="swiper-item" @click="goToPage('swiperlist1', index)">
			<image :src="item.image" mode="aspectFill" class="swiper-image" />
			<view class="swiper-title">{{item.title}}</view>
		  </view>
		</swiper-item>
	  </swiper>
	  <!-- 联系我们内容 -->
	  <view class="contact-section">
		<view class="contact-header">
		  <text class="contact-title">联系我们</text>
		  <text class="contact-subtitle">专业的助教团队随时为您服务</text>
		</view>
  
		<!-- 总店联系信息 -->
		<view v-if="headquarters.length > 0" class="headquarters-section">
		  <view class="section-title">总店联系方式</view>
		  <view v-for="(contact, index) in headquarters" :key="'hq-'+index" class="instructor-card">
			<view class="instructor-header">
			  <view class="avatar-container">
				<image :src="contact.avatarURL" mode="aspectFill" class="instructor-avatar"></image>
			  </view>
			  <view class="name-container">
				<text class="instructor-name">{{ contact.contactName }}</text>
				<text class="instructor-position">{{ contact.position }} - {{ contact.department }}</text>
			  </view>
			</view>
			<view class="instructor-content">
			  <view class="info-row" @click="makePhoneCall(contact.contactNumber)">
				<uni-icons type="phone" size="16" color="#666"></uni-icons>
				<text class="info-text">{{ contact.contactNumber }} (分机: {{ contact.phoneExtension || '无' }})</text>
			  </view>
			  <view class="info-row">
				<uni-icons type="email" size="16" color="#666"></uni-icons>
				<text class="info-text">{{ contact.email }}</text>
			  </view>
			</view>
			<view class="qrcode-section" v-if="contact.wechatQRCodeURL">
			  <image :src="contact.wechatQRCodeURL" mode="aspectFit" class="qrcode-image"></image>
			  <text class="qrcode-text">微信扫码添加</text>
			</view>
		  </view>
		</view>
  
		<!-- 助教信息列表 -->
		<view v-if="assistants.length > 0" class="assistants-list">
		  <view v-for="(assistant, index) in assistants" :key="index" class="instructor-card">
			<view class="instructor-header">
			  <view class="avatar-container">
				<image :src="assistant.avatar" mode="aspectFill" class="instructor-avatar"></image>
			  </view>
			  <view class="name-container">
				<text class="instructor-name">{{ assistant.name }}</text>
			  </view>
			</view>
			<view class="instructor-content">
			  <view class="info-row">
				<uni-icons type="clock" size="16" color="#666"></uni-icons>
				<text class="info-text">{{ assistant.workingDays }} {{ assistant.workingHours }}</text>
			  </view>
			  <view class="info-row" @click="makePhoneCall(assistant.contactNumber)">
				<uni-icons type="phone" size="16" color="#666"></uni-icons>
				<text class="info-text">{{ assistant.contactNumber }}</text>
			  </view>
			  <view class="info-row">
				<uni-icons type="email" size="16" color="#666"></uni-icons>
				<text class="info-text">{{ assistant.email }}</text>
			  </view>
			</view>
			<view class="qrcode-section" v-if="assistant.wechatQrcode">
			  <image :src="assistant.wechatQrcode" mode="aspectFit" class="qrcode-image"></image>
			  <text class="qrcode-text">微信扫码添加</text>
			</view>
		  </view>
	  </view>												
		<view v-else class="no-data">
			<uni-icons type="info-circle" size="16" color="#666"></uni-icons>
			<text>暂无助教信息</text>
		  </view>
	  </view>
	</view>
  </template>
  
  <script>
  import { generateSignature } from '@/utils/auth'
  export default {
	data() {
	  return {
		swiperlist1: [],		
		isLoggedIn: false,  // 登录状态
		username: "",       // 会员名
		assistants: [], // 助教信息列表
		headquarters: [], // 总店联系信息列表
		storeId: null, // 门店ID
		lastCallTime: null,
	  };
	},
	methods: {
		makePhoneCall(phoneNumber) {
				if (!phoneNumber) return;
				
				// 添加防抖
				if (this.lastCallTime && Date.now() - this.lastCallTime < 1000) return;
				this.lastCallTime = Date.now();
				
				uni.makePhoneCall({
					phoneNumber: phoneNumber,
					success: () => console.log('拨号成功'),
					fail: (err) => console.error('拨号失败', err)
				});
			},
	  // 获取 swiper 数据
	  async fetchSwiperData(category, listName) {
		// 先从本地存储获取数据
		  const storageKey = `swiper_${category}`;
		  const cachedData = uni.getStorageSync(storageKey);
		  
		  // 如果有缓存数据且未过期，直接使用
		  if (cachedData) {
			const { data, timestamp } = cachedData;
			// 设置缓存有效期为1小时
			if (Date.now() - timestamp < 3600000) {
			  this[listName] = data;
			  return;
			}
		  }
		this.loading = true;
		try {
		  const timestamp = Math.floor(Date.now() / 1000);
		  const nonce = Math.random().toString(36).substring(2, 10);
		  const signature = generateSignature(timestamp, nonce);
		  
		  const res = await uni.request({
			url: `https://aikexiaozhan.com/api/swiper/${category}`,
			method: "POST",
			header: {
			  'X-Timestamp': timestamp,
			  'X-Nonce': nonce,
			  'X-Signature': signature,
			  'Content-Type': 'application/json'
			},
			timeout: 5000  // 添加超时设置
		  });
		  
		  if (res.statusCode === 200) {
			this[listName] = res.data;
			// 将数据存入本地存储
			  uni.setStorageSync(storageKey, {
				data: res.data,
				timestamp: Date.now()
			  });
		  } else {
			uni.showToast({
			  title: `获取${category}数据失败`,
			  icon: 'none'
			});
		  }
		} catch (error) {
		  console.error(`请求${category}失败`, error);
		  uni.showToast({
			title: '网络请求失败，请稍后重试',
			icon: 'none'
		  });
		} finally {
		  this.loading = false;
		}
	  },  
	  // 跳转到对应页面
	  goToPage(listType, pageIndex) {
		const currentList = this[listType];
		const currentItem = currentList[pageIndex];
		if (!currentItem?.adId) {
			uni.showToast({ title: '无效的页面链接', icon: 'none' });
			return;
		}
		
		// 添加防抖
		if (this.lastClickTime && Date.now() - this.lastClickTime < 1000) return;
		this.lastClickTime = Date.now();
	  
		// 统一跳转到广告详情页，携带adId参数
		uni.navigateTo({
			url: `/pages/AdvertisingDetails/AdvertisingDetails?adId=${currentItem.adId}`
		});
	  },
	  // 获取总店联系信息
		async fetchHeadquarterContacts() {
			try {
			const timestamp = Math.floor(Date.now() / 1000);
			const nonce = Math.random().toString(36).substring(2, 10);
			const signature = generateSignature(timestamp, nonce);
			
			const res = await uni.request({
				url: 'https://aikexiaozhan.com/api/getHeadquarterContacts',
				method: 'POST',
				header: {
				'X-Timestamp': timestamp,
				'X-Nonce': nonce,
				'X-Signature': signature,
				'Content-Type': 'application/json'
				}
			});
		
			if (res.data.code === 200) {
				this.headquarters = res.data.data;
			}
			} catch (error) {
			console.error('获取总店联系信息异常:', error);
			}
		},
	   // 获取助教信息
	  async fetchAssistants() {
		try {
		  const userInfo = uni.getStorageSync('userInfo');
		  if (!userInfo || !userInfo.store || !userInfo.store.storeId) {
			console.log('未找到门店信息');
			return;
		  }
  
		  this.storeId = userInfo.store.storeId;
		  
		  // 获取签名所需参数
		  const timestamp = Math.floor(Date.now() / 1000);
		  const nonce = Math.random().toString(36).substring(2, 10);
		  const signature = generateSignature(timestamp, nonce);
  
		  const res = await uni.request({
			url: 'https://aikexiaozhan.com/api/getStoreAssistants',
			method: 'POST',
			data: {
			  storeId: this.storeId
			},
			header: {
			  'X-Timestamp': timestamp,
			  'X-Nonce': nonce,
			  'X-Signature': signature,
			  'Content-Type': 'application/json'
			}
		  });
  
		  if (res.data.code === 200) {
			this.assistants = res.data.assistants;
		  } else {
			console.error('获取助教信息失败:', res.data.message);
		  }
		} catch (error) {
		  console.error('获取助教信息异常:', error);
		}
	  },	  
	  // 跳转到注册页面
	  goToRegister() {
		uni.navigateTo({
		  url: '/pages/register/register' // 注册页面路径
		});
	  },
	  // 检查用户登录状态
	  checkLoginStatus() {
		const userInfo = uni.getStorageSync('userInfo');
		if (userInfo) {
		  this.isLoggedIn = true;
		  this.username = userInfo.username;
		} else {
		  this.isLoggedIn = false;
		}
	  }
	},
	mounted() {
	  // 初始化时检查登录状态
	  this.checkLoginStatus();
	  this.fetchAssistants(); // 获取助教信息
	  this.fetchHeadquarterContacts(); // 获取总店联系信息
	  this.fetchSwiperData("swiperlist1", "swiperlist1");
	}
  };
  </script>
  
  <style scoped>
  .header-title {
	width: 100%;
	height: 132px; /* 调整为与小程序胶囊按钮相同高度 */
	line-height: 132px; /* 确保文字垂直居中 */
	font-family: PingFang-SC, PingFang-SC;
	font-weight: 500;
	font-size: 16px;
	color: #FFFFFF;
	text-align: left;
	font-style: normal;
	text-transform: none;
	background-color: #F7664A;
	padding: 0 10px; /* 只保留左右padding */
	margin: 0 auto;
	box-sizing: border-box; /* 确保padding不影响总高度 */
  }	
  .swiper-container {
	height: 200px;
	position: relative; /* 添加相对定位 */
	top: -30px; /* 上移30px与标题重叠 */
	margin-top: 0; /* 移除原有上边距 */
	width: calc(100% - 20px);
	margin: 0 10px;
  }
  
  .swiper-item {
	height: 100%;
	position: relative;
	width: 100%; /* 添加宽度100% */
  }
  
  .swiper-image {
	width: 100%;
	height: 100%;
  }
  
  .swiper-title {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	color: white;
	text-align: left;
	background-color: rgba(0, 0, 0, 0.5);
	padding: 10px;
	font-size: 16px; /* 添加字体大小 */
	white-space: nowrap; /* 防止标题换行 */
	overflow: hidden;
	text-overflow: ellipsis; /* 超出部分显示省略号 */
  }		
  .container {
	min-height: 100vh;
	background-color: #F7664A;
	padding-bottom: 100px; /* 为底部导航留出空间 */
  }	
  
  /* 联系我们内容样式 */
  .instructor-header {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 12px;
  }
  
  .avatar-container {
	margin-bottom: 8px;
  }
  
  .name-container {
	text-align: center;
  }
  .contact-section {
	padding: 24px;
	margin: 16px;
	border-radius: 16px;
	background: #fff;
	box-shadow: 0 2px 16px rgba(0, 0, 0, 0.05);
  }
  
  .contact-header {
	text-align: center;
	margin-bottom: 30px;
  }
  
  .contact-title {
	font-size: 22px;
	color: #333;
	position: relative;
	padding-bottom: 8px;
  }
  
  
  .contact-subtitle {
	font-size: 14px;
	color: #666;
	display: block;
  }
  .instructor-avatar {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	border: 3px solid #F7664A;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  .instructor-info {
	flex: 1;
  }
  
  .qrcode-section {
	margin-top: 20px;
	padding: 16px;
	background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
	border-radius: 12px;
	text-align: center;
  }
  
  .qrcode-image {
	width: 180px;
	height: 180px;
	margin: 0 auto;
	border: 1px solid #eee;
	border-radius: 8px;
	background: #fff;
	padding: 8px;
  }
  
  .qrcode-text {
	display: block;
	margin-top: 8px;
	font-size: 12px;
	color: #666;
  }
  .instructor-card {
	background: #fff;
	border-radius: 16px;
	padding: 24px;
	margin-bottom: 20px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
  }
  
  .instructor-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  }
  .instructor-header {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 16px;
  }
  
  .instructor-name {
	font-size: 20px;
	font-weight: 600;
	color: #333;
	margin-top: 12px;
	text-align: center;
  }
  
  /* 助教列表样式 */
  .assistants-list {
	margin-top: 20px;
  }
  .instructor-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8px 0;
  }
  
  .info-row {
	display: flex;
	align-items: center;
	margin-bottom: 14px;
	padding: 10px 16px;
	background: #f9f9f9;
	border-radius: 8px;
	width: 100%;
	transition: all 0.2s ease;
  }
  .info-row:active {
	background: #f0f0f0;
  }
  .info-text {
	margin-left: 8px;
	color: #666;
	font-size: 14px;
  }
  
  .no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60px 0;
	color: #999;
  }
  
  .no-data text {
	margin-top: 16px;
	font-size: 16px;
  }
  
  /* 响应式调整 */
  @media screen and (min-width: 768px) {
	.assistants-list {
	  display: grid;
	  grid-template-columns: repeat(2, 1fr);
	  gap: 16px;
	}
  }
  
  /* 底部栏目样式 */
  .u-tabbar {
	background-color: #fff;
	border-top: 1px solid #ccc;
  }
  .section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 20px 0 10px;
  padding-left: 10px;
  border-left: 4px solid #F7664A;
}

.instructor-position {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}
</style>
