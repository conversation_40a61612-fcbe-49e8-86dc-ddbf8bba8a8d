//+------------------------------------------------------------------+
//|                                  MultiTimeframeTrendEA.mq4       |
//|                        Copyright 2024, Developed by AI Assistant |
//|                                             https://www.example.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      "https://www.example.com"
#property version   "1.20"
#property strict

//--- 输入参数
input double   RiskPercent    = 1.0;       // 风险比例（账户净值%）
input int      MaxDailyLoss   = 3;         // 单日最大亏损（%）
input int      TrailingStop   = 50;        // 追踪止损点数（0=禁用）
input int      BreakEven      = 30;        // 保本点数（盈利后移动止损）
input bool     EnableNewsFilter = true;    // 启用新闻过滤器
input int      TradeStartHour = 3;         // 允许交易开始小时（服务器时间）
input int      TradeEndHour   = 21;        // 允许交易结束小时

//--- 全局变量
int    MagicNumber = 159357;               // EA唯一标识
double ATRValue;                           // 动态波动率值
datetime LastTradeTime = 0;                // 最后交易时间

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化检查
   if(Digits() < 4 || Digits() > 5){
      Alert("不支持的货币对小数点位数!");
      return(INIT_FAILED);
   }
   // 设置定时器（每小时检查新闻事件）
   EventSetTimer(3600);
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   EventKillTimer();
}

//+------------------------------------------------------------------+
//| 定时器事件（新闻检查）                                           |
//+------------------------------------------------------------------+
void OnTimer()
{
   if(EnableNewsFilter && IsHighImpactNewsPending())
   {
      CloseAllPositions();
      Print("检测到重要新闻事件，平仓所有头寸");
   }
}

//+------------------------------------------------------------------+
//| 主交易逻辑                                                       |
//+------------------------------------------------------------------+
void OnTick()
{
   // 基础环境检查
   if(!IsTradingAllowed()) return;          // 检查交易时间/账户状态
   if(IsHedgingDisabled()) return;         // 检查对冲模式
   if(IsMaxDailyLossReached()) return;     // 风控检查

   // 更新动态参数
   ATRValue = iATR(Symbol(), PERIOD_H1, 14, 0); // 获取当前ATR值

   // 核心策略逻辑
   CheckForEntrySignal();
   ManageOpenPositions();
}

//+------------------------------------------------------------------+
//| 入场信号检测                                                     |
//+------------------------------------------------------------------+
void CheckForEntrySignal()
{
   // 多时间框架趋势确认
   bool trendUp = IsTrendUp(PERIOD_H4) && IsMomentumUp(PERIOD_H1);
   bool trendDown = IsTrendDown(PERIOD_H4) && IsMomentumDown(PERIOD_H1);

   // 入场条件过滤
   if(trendUp && IsPullbackValid(PERIOD_M15))
   {
      double lot = CalculateLotSize(RiskPercent);
      OpenPosition(OP_BUY, lot);
   }
   else if(trendDown && IsPullbackValid(PERIOD_M15))
   {
      double lot = CalculateLotSize(RiskPercent);
      OpenPosition(OP_SELL, lot);
   }
}

//+------------------------------------------------------------------+
//| 趋势方向判断（H4级别）                                           |
//+------------------------------------------------------------------+
bool IsTrendUp(int timeframe)
{
   double ema50 = iMA(NULL, timeframe, 50, 0, MODE_EMA, PRICE_CLOSE, 0);
   double ema200 = iMA(NULL, timeframe, 200, 0, MODE_EMA, PRICE_CLOSE, 0);
   return (ema50 > ema200) && (Close[0] > ema50);
}

//+------------------------------------------------------------------+
//| 动量确认（H1级别）                                               |
//+------------------------------------------------------------------+
bool IsMomentumUp(int timeframe)
{
   double macdMain = iMACD(NULL, timeframe, 12, 26, 9, PRICE_CLOSE, MODE_MAIN, 0);
   double macdSignal = iMACD(NULL, timeframe, 12, 26, 9, PRICE_CLOSE, MODE_SIGNAL, 0);
   return (macdMain > macdSignal) && (macdMain > 0);
}

//+------------------------------------------------------------------+
//| 回撤确认（M15级别）                                              |
//+------------------------------------------------------------------+
bool IsPullbackValid(int timeframe)
{
   double upperBand = iBands(NULL, timeframe, 20, 2, 0, PRICE_CLOSE, MODE_UPPER, 0);
   double lowerBand = iBands(NULL, timeframe, 20, 2, 0, PRICE_CLOSE, MODE_LOWER, 0);
   return (Close[0] < upperBand && Close[0] > lowerBand); // 价格在布林带通道内
}

//+------------------------------------------------------------------+
//| 智能仓位管理                                                     |
//+------------------------------------------------------------------+
void ManageOpenPositions()
{
   for(int i=OrdersTotal()-1; i>=0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol())
         {
            // 追踪止损逻辑
            if(TrailingStop > 0) AdjustTrailingStop(OrderTicket());
            
            // 保本逻辑
            if(BreakEven > 0) MoveToBreakEven(OrderTicket());
            
            // 强制离场条件
            if(IsForceExitCondition(OrderType()))
               OrderClose(OrderTicket(), OrderLots(), OrderClosePrice(), 3);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 动态手数计算（基于风险百分比）                                   |
//+------------------------------------------------------------------+
double CalculateLotSize(double riskPercent)
{
   double riskAmount = AccountEquity() * (riskPercent / 100);
   double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
   double stopLoss = ATRValue * 2; // 2倍ATR作为止损
   
   if(Digits() == 3 || Digits() == 5) stopLoss *= 10; // 处理JPY货币对
   
   double lotSize = (riskAmount / (stopLoss * tickValue));
   lotSize = MathFloor(lotSize * 100) / 100; // 标准化手数
   return MathMin(lotSize, MarketInfo(Symbol(), MODE_MAXLOT));
}

//+------------------------------------------------------------------+
//| 开仓函数（带异常处理）                                           |
//+------------------------------------------------------------------+
void OpenPosition(int orderType, double lotSize)
{
   if(OrdersTotal() >= 3) return; // 最大同时持仓3单

   double price = (orderType == OP_BUY) ? Ask : Bid;
   double sl = (orderType == OP_BUY) ? price - ATRValue*2 : price + ATRValue*2;
   double tp = (orderType == OP_BUY) ? price + ATRValue*4 : price - ATRValue*4;

   int ticket = OrderSend(Symbol(), orderType, lotSize, price, 3, sl, tp, "", MagicNumber);
   
   if(ticket < 0)
      Print("开仓失败! 错误代码:", GetLastError());
   else
      LastTradeTime = TimeCurrent();
}

//+------------------------------------------------------------------+
//| 追踪止损管理                                                     |
//+------------------------------------------------------------------+
void AdjustTrailingStop(int ticket)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET)) return;
   
   double newSl = 0;
   double currentSl = OrderStopLoss();
   
   if(OrderType() == OP_BUY){
      newSl = Bid - TrailingStop*Point;
      if(newSl > currentSl || currentSl == 0)
         OrderModify(ticket, OrderOpenPrice(), newSl, OrderTakeProfit(), 0);
   }
   else if(OrderType() == OP_SELL){
      newSl = Ask + TrailingStop*Point;
      if(newSl < currentSl || currentSl == 0)
         OrderModify(ticket, OrderOpenPrice(), newSl, OrderTakeProfit(), 0);
   }
}

//+------------------------------------------------------------------+
//| 风险控制模块                                                     |
//+------------------------------------------------------------------+
bool IsMaxDailyLossReached()
{
   double dailyProfit = AccountEquity() - AccountBalance();
   double lossPercent = (-dailyProfit / AccountBalance()) * 100;
   return (lossPercent >= MaxDailyLoss);
}

//+------------------------------------------------------------------+
//| 环境检查                                                         |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
{
   // 交易时段检查
   int currentHour = Hour();
   if(currentHour < TradeStartHour || currentHour >= TradeEndHour) return false;
   
   // 新订单冷却时间
   if(TimeCurrent() - LastTradeTime < 30*60) return false; // 30分钟冷却
   
   // 周五平仓逻辑
   if(DayOfWeek() == FRIDAY && Hour() >= 21) CloseAllPositions();
   
   return (IsTradeAllowed() && !IsExpertEnabled());
}

//+------------------------------------------------------------------+
//| 其他辅助函数（需自行实现）                                       |
//+------------------------------------------------------------------+
bool IsHighImpactNewsPending() { /* 集成经济日历API */ }
void CloseAllPositions() { /* 平仓所有订单 */ }
bool IsForceExitCondition(int orderType) { /* MACD背离检测等 */ }
void MoveToBreakEven(int ticket) { /* 保本逻辑 */ }




