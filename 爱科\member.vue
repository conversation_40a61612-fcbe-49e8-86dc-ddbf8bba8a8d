<template>
  <view>
    <swiper
      class="swiper-container"
      :autoplay="true"
      :circular="true"
      :indicator-dots="true"
      indicator-active-color="#007AFF"
      indicator-color="#CCC"
      :interval="3000"
      :duration="500">
      <swiper-item v-for="(item, index) in swiperlist1" :key="index">
    	<view class="swiper-item" @click="goToPage('swiperlist1', index)">
    	  <image :src="item.image" mode="aspectFill" class="swiper-image" />
    	  <view class="swiper-title">{{item.title}}</view>
    	</view>
      </swiper-item>
    </swiper>
	
    <!-- 会员信息展示 -->
    <view class="member-details">
      <view class="info-card">
        <view class="detail-item">
          <text class="label">登录手机号:</text>
          <text class="value">{{ userInfo.contactNumber }}</text>
        </view>

        <view class="detail-item">
          <text class="label">家长姓名:</text>
          <text class="value">{{ userInfo.parentName }}</text>
        </view>

        <view class="detail-item">
          <text class="label">所在门店:</text>
          <text class="value">{{ storeName }} - {{ storeAddress }}</text>
        </view>
      </view>

      <view class="card-list">
        <text class="section-title">会员卡信息</text>
        <view v-for="(card, index) in memberCards" :key="index" class="card-item">
          <view class="card-header">
            <text class="card-type">{{ card.cardType }}</text>
            <text class="card-status" :class="card.status === '有效' ? 'status-active' : 'status-inactive'">
              {{ card.status }}
            </text>
          </view>
          <view class="card-body">
            <view class="card-info">
              <text class="label">会员卡号</text>
              <text class="value">{{ card.cardNumber }}</text>
            </view>
            <view class="card-info">
              <text class="label">有效期</text>
              <text class="value">{{ card.startDate }} 至 {{ card.endDate }}</text>
            </view>
            <view v-if="card.childInfo" class="card-info">
              <text class="label">使用人</text>
              <text class="value">{{ card.childInfo.name }} ({{ card.childInfo.grade || '未填写年级' }})</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { generateSignature } from '@/utils/auth'
export default {
  data() {
    return {
      swiperlist1: [],		
      isLoggedIn: false,  // 登录状态
      username: "",       // 会员名
  storeName: '',
  storeAddress: '',
      userInfo: {},
  memberCards: []
    };
  },
  methods: {
   // 获取 swiper 数据
   async fetchSwiperData(category, listName) {
     // 先从本地存储获取数据
   	const storageKey = `swiper_${category}`;
   	const cachedData = uni.getStorageSync(storageKey);
   	
   	// 如果有缓存数据且未过期，直接使用
   	if (cachedData) {
   	  const { data, timestamp } = cachedData;
   	  // 设置缓存有效期为1小时
   	  if (Date.now() - timestamp < 3600000) {
   		this[listName] = data;
   		return;
   	  }
   	}
     this.loading = true;
     try {
       const timestamp = Math.floor(Date.now() / 1000);
       const nonce = Math.random().toString(36).substring(2, 10);
       const signature = generateSignature(timestamp, nonce);
       
       const res = await uni.request({
         url: `https://aikexiaozhan.com/api/swiper/${category}`,
         method: "POST",
         header: {
           'X-Timestamp': timestamp,
           'X-Nonce': nonce,
           'X-Signature': signature,
           'Content-Type': 'application/json'
         },
         timeout: 5000  // 添加超时设置
       });
       
       if (res.statusCode === 200) {
         this[listName] = res.data;
   	  // 将数据存入本地存储
   		uni.setStorageSync(storageKey, {
   		  data: res.data,
   		  timestamp: Date.now()
   		});
       } else {
         uni.showToast({
           title: `获取${category}数据失败`,
           icon: 'none'
         });
       }
     } catch (error) {
       console.error(`请求${category}失败`, error);
       uni.showToast({
         title: '网络请求失败，请稍后重试',
         icon: 'none'
       });
     } finally {
       this.loading = false;
     }
   }, 
   // 优化后的页面跳转方法log
   goToPage(listType, pageIndex) {
     const currentList = this[listType];
     const currentItem = currentList[pageIndex];
     if (!currentItem?.adId) {
         uni.showToast({ title: '无效的页面链接', icon: 'none' });
         return;
     }
     
     // 添加防抖
     if (this.lastClickTime && Date.now() - this.lastClickTime < 1000) return;
     this.lastClickTime = Date.now();
   
     // 统一跳转到广告详情页，携带adId参数
     uni.navigateTo({
         url: `/pages/AdvertisingDetails/AdvertisingDetails?adId=${currentItem.adId}`
     });
   },
   // 新增获取会员卡信息方法
   async fetchMemberCards() {
   try {
     const openid = uni.getStorageSync('openid')
     if (!openid) return
 
     const timestamp = Math.floor(Date.now() / 1000)
     const nonce = Math.random().toString(36).substring(2, 10)
     const signature = generateSignature(timestamp, nonce)
 
     const res = await uni.request({
     url: 'https://aikexiaozhan.com/api/getMemberCardInfo',
     method: 'POST',
     data: { openid },
     header: {
       'X-Timestamp': timestamp,
       'X-Nonce': nonce,
       'X-Signature': signature,
       'Content-Type': 'application/json'
     }
     })
 
     if (res.statusCode === 200 && res.data.code === 200) {
     this.memberCards = res.data.cards
     }
   } catch (error) {
     console.error('获取会员卡信息失败:', error)
   }
   },
    // 处理tabbar点击切换
    onTabChange(value) {
      const tabUrls = [
        '/pages/index/index',      // 首页
        '/pages/review/review',    // 复习
        '/pages/parent/parent',    // 家长
        '/pages/contact/contact',  // 联系
        '/pages/member/member'     // 会员
      ];
      uni.navigateTo({
        url: tabUrls[value]  // 根据点击的tab跳转到对应页面
      });
    },

    // 检查用户登录状态
    checkLoginStatus() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        this.isLoggedIn = true;
    this.username = userInfo.parentName;
    this.userInfo = {
    ...userInfo
    };
    // 提取门店信息
    if (userInfo.store) {
    this.storeName = userInfo.store.storeName || '';
    this.storeAddress = userInfo.store.address || '';
    }
      } else {
        this.isLoggedIn = false;
        // 未登录时自动跳转到注册页
        uni.redirectTo({
          url: '/pages/register/register'
        });
      }
    }
  },
  mounted() {
    // 初始化时检查登录状态
    this.checkLoginStatus();
    this.fetchMemberCards();
    this.fetchSwiperData("swiperlist1", "swiperlist1");
  }
};
</script>

<style scoped>
.swiper-container {
  height: 250px;
  width: 100%; /* 添加宽度100% */
}

.swiper-item {
  height: 100%;
  position: relative;
  width: 100%; /* 添加宽度100% */
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.swiper-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  color: white;
  text-align: left;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10px;
  font-size: 16px; /* 添加字体大小 */
  white-space: nowrap; /* 防止标题换行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}		
/* 会员信息栏样式 */
.member-info {
  padding: 20px;
  text-align: center;
  background-color: #f8f8f8;
  border-top: 1px solid #ccc;
}

.member-info text {
  font-size: 16px;
  color: #333;
}

.member-info button {
  font-size: 16px;
  color: #007AFF;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

/* 会员详情容器 */
.member-details {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 基本信息卡片 */
.info-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 详情项样式 */
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

/* 标签和值的样式 */
.label {
  color: #666;
  font-size: 14px;
}

.value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

/* 会员卡列表样式 */
.card-list {
  margin-top: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-left: 12px;
  border-left: 4px solid #007AFF;
}

/* 会员卡项样式 */
.card-item {
  background: #ffffff;
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 卡片头部 */
.card-header {
  background: linear-gradient(135deg, #007AFF, #1E90FF);
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-type {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.card-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
}

.status-active {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.status-inactive {
  background-color: rgba(0, 0, 0, 0.1);
  color: #ffffff;
}

/* 卡片主体 */
.card-body {
  padding: 16px;
}

.card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-info:last-child {
  margin-bottom: 0;
}

/* 底部导航栏样式优化 */
.u-tabbar {
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}
</style>
