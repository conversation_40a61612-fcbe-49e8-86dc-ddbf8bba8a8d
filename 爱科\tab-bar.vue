<template>
    <u-tabbar
      :value="currentTab"
      @change="handleTabChange"
      :fixed="true"
      :placeholder="true"
      :safeAreaInsetBottom="true"
    >
      <u-tabbar-item text="首页" icon="home"></u-tabbar-item>
      <u-tabbar-item text="复习" icon="play-right"></u-tabbar-item>
      <u-tabbar-item text="家长" icon="grid"></u-tabbar-item>
      <u-tabbar-item text="联系" icon="phone"></u-tabbar-item>
      <u-tabbar-item text="会员" icon="account"></u-tabbar-item>
    </u-tabbar>
  </template>
  
  <script>
  export default {
    name: 'TabBar',
    data() {
      return {
        routes: [
          '/pages/index/index',
          '/pages/review/review',
          '/pages/parent/parent',
          '/pages/contact/contact',
          '/pages/member/member'
        ]
      }
    },
    computed: {
      // 根据当前路由计算激活的标签
      currentTab() {
        const currentPage = this.getCurrentPage()
        return this.routes.findIndex(route => route === `/${currentPage}`)
      }
    },
    methods: {
      getCurrentPage() {
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        return currentPage?.route || 'pages/index/index'
      },
      handleTabChange(index) {
        if (index >= 0 && index < this.routes.length) {
          uni.switchTab({
            url: this.routes[index]
          })
        }
      }
    }
  }
  </script>
  
  <style scoped>
  .u-tabbar {
    background-color: #fff;
    border-top: 1px solid #eaeaea;
    box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.04);
  }
  </style>