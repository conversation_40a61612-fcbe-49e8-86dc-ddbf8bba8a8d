<template>
	<view>
	  <swiper
	    class="swiper-container"
	    :autoplay="true"
	    :circular="true"
	    :indicator-dots="true"
	    indicator-active-color="#007AFF"
	    indicator-color="#CCC"
	    :interval="3000"
	    :duration="500">
	    <swiper-item v-for="(item, index) in swiperlist1" :key="index">
	  	<view class="swiper-item" @click="goToPage('swiperlist1', index)">
	  	  <image :src="item.image" mode="aspectFill" class="swiper-image" />
	  	  <view class="swiper-title">{{item.title}}</view>
	  	</view>
	    </swiper-item>
	  </swiper>
		<!-- 获取手机号按钮 -->
		<view class="phone-button-container">
		  <button 
			open-type="getPhoneNumber" 
			@getphonenumber="onGetPhoneNumber" 
			class="phone-button"
		  >
			获取手机号
		  </button>
		</view>
		<view class="form-container">
		  <form @submit="submitForm" report-submit>
			<view class="form-item">
			  <text class="form-label">家长姓名：</text>
			  <input type="text" v-model="formData.parentName" placeholder="请输入家长姓名" />
			</view>
  
			<view class="form-item">
			  <text class="form-label">联系电话：</text>
			  <input type="number" v-model="formData.contactNumber" placeholder="请点击上方按钮获取手机号" :disabled="true" />
			</view>
			<view class="form-item">
			  <text class="form-label">门店：</text>
			  <picker 
			    mode="selector" 
			    :range="stores.map(store => store.storeName)"
			    @change="handleStoreChange"
			  >
			    <view class="pickergrade">
				  {{ formData.shopId ? (stores.find(store => store.storeCode === formData.shopId) || {}).storeName || '请选择门店' : '请选择门店' }}
			    </view>
			  </picker>
			</view>
<!-- 			<view class="form-item">
			  <text class="form-label">紧急联系电话：</text>
			  <input type="number" v-model="formData.emergencyContact" placeholder="请输入紧急联系电话" />
			</view> -->
  
		  <!-- 动态孩子信息 -->
<!-- 		  <view v-for="(child, index) in formData.children" :key="index" class="child-container">
			<view class="form-item">
			  <text class="form-label">孩子姓名：</text>
			  <input type="text" v-model="child.name" placeholder="请输入孩子姓名" />
			</view>
			<view class="form-item">
			  <text class="form-label">性别：</text>
			  <picker mode="selector" :range="genderOptions" @change="handleGenderChange($event, index)">
				<view class="pickergender">{{ child.gender || '性别' }}</view>
			  </picker>
			</view>
			<view class="form-item">
			  <text class="form-label">就读学校：</text>
			  <input type="text" v-model="child.school" placeholder="请输入就读学校" />
			</view>
			<view class="form-item">
			  <text class="form-label">现读年级：</text>
			  <picker mode="selector" :range="gradeOptions" @change="handleGradeChange($event, index)">
				<view class="pickergrade">{{ child.grade || '请选择年级' }}</view>
			  </picker>
			</view>
			<button class="btn btn-remove" @click.prevent="removeChild(index)">删除</button>
		  </view> -->
  
		  <!-- 添加孩子按钮 -->
<!-- 		  <button class="btn btn-add" @click.prevent="addChild">新增孩子</button> -->
  
			<view class="button-container">
			  <button class="btn" @click="goBack">返回</button>
			  <button form-type="submit" class="btn btn-submit">提交</button>
			</view>
		  </form>
		</view>
	</view>
  </template>
  
  <script>
  import { generateSignature } from '@/utils/auth'
  import md5 from 'md5'
  import sha1 from 'sha1'
  export default {
	data() {
	  return {
		swiperlist1: [],
		stores: [], //门店列表
		formData: {
		  parentName: '', // 家长姓名
		  contactNumber: '', // 联系电话
		  emergencyContact: '', // 紧急联系电话
		  shopId: '', // 门店编号
		  children: [
					{ name: '', gender: '', school: '', grade: '' } // 初始一个孩子字段
				  ]
		},
		genderOptions: ["男", "女"], // 性别选项
		gradeOptions: [
		  "幼儿园小班", "幼儿园中班", "幼儿园大班", 
		  "小学一年级", "小学二年级", "小学三年级", "小学四年级", "小学五年级", "小学六年级",
		  "初中一年级", "初中二年级", "初中三年级",
		  "高中一年级", "高中二年级", "高中三年级"
		],	  
	  };
	},
	onLoad() {
	  // 检查是否已登录
	  const userInfo = uni.getStorageSync('userInfo');
	 /* if (userInfo && userInfo.isLoggedIn) {
		uni.showToast({ 
			  title: '已经登录！', 
			  icon: 'success',
			  duration: 1500,
			  success: () => {
				setTimeout(() => {
				  uni.reLaunch({
					url: '/pages/index/index'
				  });
				}, 1500);
			  }
			});
	  } */
	  this.fetchSwiperData("swiperlist1", "swiperlist1");
	  this.fetchStores(); 
	},
	methods: {
		  // 获取 swiper 数据
		  async fetchSwiperData(category, listName) {
		    // 先从本地存储获取数据
		  	const storageKey = `swiper_${category}`;
		  	const cachedData = uni.getStorageSync(storageKey);
		  	
		  	// 如果有缓存数据且未过期，直接使用
		  	if (cachedData) {
		  	  const { data, timestamp } = cachedData;
		  	  // 设置缓存有效期为1小时
		  	  if (Date.now() - timestamp < 3600000) {
		  		this[listName] = data;
		  		return;
		  	  }
		  	}
		    this.loading = true;
		    try {
		      const timestamp = Math.floor(Date.now() / 1000);
		      const nonce = Math.random().toString(36).substring(2, 10);
		      const signature = generateSignature(timestamp, nonce);
		      
		      const res = await uni.request({
		        url: `https://aikexiaozhan.com/api/swiper/${category}`,
		        method: "POST",
		        header: {
		          'X-Timestamp': timestamp,
		          'X-Nonce': nonce,
		          'X-Signature': signature,
		          'Content-Type': 'application/json'
		        },
		        timeout: 5000  // 添加超时设置
		      });
		      
		      if (res.statusCode === 200) {
		        this[listName] = res.data;
		  	  // 将数据存入本地存储
		  		uni.setStorageSync(storageKey, {
		  		  data: res.data,
		  		  timestamp: Date.now()
		  		});
		      } else {
		        uni.showToast({
		          title: `获取${category}数据失败`,
		          icon: 'none'
		        });
		      }
		    } catch (error) {
		      console.error(`请求${category}失败`, error);
		      uni.showToast({
		        title: '网络请求失败，请稍后重试',
		        icon: 'none'
		      });
		    } finally {
		      this.loading = false;
		    }
		  },  
		  async fetchStores() {
		      try {
		        const timestamp = Math.floor(Date.now() / 1000);
		        const nonce = Math.random().toString(36).substring(2, 10);
		        const signature = generateSignature(timestamp, nonce);
		        
		        const res = await uni.request({
		          url: 'https://aikexiaozhan.com/api/getStores',
		          method: 'POST',
		          header: {
		            'X-Timestamp': timestamp,
		            'X-Nonce': nonce,
		            'X-Signature': signature,
		            'Content-Type': 'application/json'
		          }
		        });
		        
		        if (res.statusCode === 200 && res.data.code === 200) {
		          this.stores = res.data.stores;
		        } else {
		          console.error('获取门店信息失败:', res);
		        }
		      } catch (error) {
		        console.error('请求门店信息失败:', error);
		      }
		    },
		  async fetchDefaultShop() {
		      try {
		        //const phone = this.formData.contactNumber || '';
				const phone = '15158985117';
		        if (!phone) {
					console.log('缺少必要参数');
					return;
		        }
		        const res = await uni.request({
		          url: 'https://edsyn.xin/wx_api/user_shops',
		          method: 'POST',
		          header: {
		            'Content-Type': 'application/json'
		          },
		          data: { 
					  phone: phone
				  }
		        });
				console.log(res.data.data);
		        if (res.data && res.data.data) {
		          this.formData.shopId = res.data.data;
		        }
		      } catch (error) {
		        //console.error('获取默认门店失败:', error);
		      }
		    },
		  addChild() {
			this.formData.children.push({ name: '', gender: '', school: '', grade: '' });
		  },
		  removeChild(index) {
			this.formData.children.splice(index, 1);
		  },	  
		  handleGenderChange(event, index) {
			const selectedGender = this.genderOptions[event.detail.value];
			this.$set(this.formData.children[index], 'gender', selectedGender);
		  },
		  handleGradeChange(event, index) {
			const selectedGrade = this.gradeOptions[event.detail.value];
			this.$set(this.formData.children[index], 'grade', selectedGrade);
		  },   
		  // 跳转到对应页面
		  goBack() {
			// 返回上一页
			uni.navigateBack();
		  },	  
		  async submitForm() {
			  console.log("提交的表单数据：", this.formData);
			  
			  // 获取 OPENID
			  const openid = uni.getStorageSync('openid');
			  if (!openid) {
				  uni.showToast({ title: '请先获取用户信息', icon: 'none' });
				  return;
			  }
			  // 将 OPENID 添加到表单数据
			  this.formData.openid = openid;
		  
			  // 表单验证
			  if (!this.formData.parentName) {
				uni.showToast({ title: '请输入家长姓名', icon: 'none' });
				return;
			  }
			  if (!this.formData.contactNumber) {
				uni.showToast({ title: '请获取手机号', icon: 'none' });
				return;
			  }
			 //  if (!this.formData.emergencyContact) {
				// uni.showToast({ title: '请输入紧急联系电话', icon: 'none' });
				// return;
			 //  }
			 //  if (this.formData.children.some(child => !child.name || !child.gender || !child.school || !child.grade)) {
				// uni.showToast({ title: '请填写完整的孩子信息', icon: 'none' });
				// return;
			 //  }
		  
			  try {
				const timestamp = Math.floor(Date.now() / 1000)
				const nonce = Math.random().toString(36).substring(2, 10)
				const signature = generateSignature(timestamp, nonce)
				const res = await uni.request({
				  url: 'https://aikexiaozhan.com/api/submitRegisterForm', // 替换为实际的 API 地址
				  method: 'POST',
				  data: this.formData,
				  header: {
					'X-Timestamp': timestamp,
					'X-Nonce': nonce,
					'X-Signature': signature,
					'Content-Type': 'application/json'
				  }
				});
		  
				if (res.statusCode === 200) {
				  // 存储用户信息到本地
				  uni.setStorageSync('userInfo', {
					parentName: this.formData.parentName,
					contactNumber: this.formData.contactNumber,
					isLoggedIn: true
				  });
		  
				  uni.showToast({ 
				        title: '提交成功！', 
				        icon: 'success',
				        duration: 1500,
				        success: () => {
				          setTimeout(() => {
				            uni.reLaunch({
				              url: '/pages/index/index'
				            });
				          }, 1500);
				        }
				      });
				} else {
				  uni.showToast({ title: '提交失败，请重试', icon: 'none' });
				}
			  } catch (error) {
				console.error('提交表单时发生错误:', error);
				uni.showToast({ title: '提交失败，请重试', icon: 'none' });
			  }
			},
		// 优化后的页面跳转方法log
		goToPage(listType, pageIndex) {
		  const currentList = this[listType];
		  const currentItem = currentList[pageIndex];
		  if (!currentItem?.adId) {
		      uni.showToast({ title: '无效的页面链接', icon: 'none' });
		      return;
		  }
		  
		  // 添加防抖
		  if (this.lastClickTime && Date.now() - this.lastClickTime < 1000) return;
		  this.lastClickTime = Date.now();
		
		  // 统一跳转到广告详情页，携带adId参数
		  uni.navigateTo({
		      url: `/pages/AdvertisingDetails/AdvertisingDetails?adId=${currentItem.adId}`
		  });
		},
		async onGetPhoneNumber(e) {
			if (e.detail.errMsg !== 'getPhoneNumber:ok') {
			  uni.showToast({ title: '获取手机号失败', icon: 'none' });
			  return;
			}		  
			try {
			  const timestamp = Math.floor(Date.now() / 1000)
			  const nonce = Math.random().toString(36).substring(2, 10)
			  const signature = generateSignature(timestamp, nonce)
			  const res = await uni.request({
				url: 'https://aikexiaozhan.com/api/getphonenumber',
				method: 'POST',
				header: {
				  'X-Timestamp': timestamp,
				  'X-Nonce': nonce,
				  'X-Signature': signature,
				  'Content-Type': 'application/json'
				},
				data: { code: e.detail.code }
			  });
			  if (res.data.phoneNumber) {
				  this.formData.contactNumber = res.data.phoneNumber; // 将手机号填充到联系电话字段
				  uni.showToast({ title: '手机号获取成功', icon: 'success' });
				  this.fetchDefaultShop();
				} else {
				  uni.showToast({ title: '获取手机号失败', icon: 'none' });
				}
			} catch (error) {
			  uni.showToast({ title: '解密失败', icon: 'none' });
			}		  
		},
	}
  };
  </script>
  <style scoped>
.swiper-container {
  height: 250px;
  width: 100%; /* 添加宽度100% */
}

.swiper-item {
  height: 100%;
  position: relative;
  width: 100%; /* 添加宽度100% */
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.swiper-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  color: white;
  text-align: left;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10px;
  font-size: 16px; /* 添加字体大小 */
  white-space: nowrap; /* 防止标题换行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}	  
  .form-container {
	margin: 0 auto; /* 保持居中 */
	padding: 20px;
  }
  
  .form-item {
	margin-bottom: 20px;
	display: flex;
	flex-direction: column; /* 保持标签和输入框垂直布局 */
  }
  
  .form-label {
	margin-bottom: 5px;
	font-size: 16px;
	color: #333;
  }
  
  input {
	width: 90%; /* 输入框宽度改为90%，留出右侧空白 */
	height: 40px;
	padding: 5px 10px;
	font-size: 14px;
	border: 1px solid #ccc;
	border-radius: 5px;
  }
  
  .child-container {
	border: 1px solid #ccc;
	border-radius: 5px;
	margin-bottom: 20px;
	padding: 10px;
	background-color: #f9f9f9;
  }
  
  .btn-add {
	width: 100%;
	height: 40px;
	font-size: 16px;
	background-color: #007AFF;
	color: white;
	border: none;
	border-radius: 5px;
	margin-bottom: 20px;
  }
  
  .btn-remove {
	width: 100%;
	height: 40px;
	font-size: 16px;
	background-color: #FF5733;
	color: white;
	border: none;
	border-radius: 5px;
	margin-top: 10px;
  }
  
  .pickergender {
	width: 80px; /* 设置固定宽度 */
	height: 40px;
	line-height: 40px;
	text-align: center;
	border: 1px solid #ccc;
	border-radius: 5px;
	background-color: #f9f9f9;
	margin-left: 10px; /* 添加间距 */
  }
  
  .pickergrade {
	width: 90%; /* 宽度调整为全宽，保持一致 */
	height: 40px;
	line-height: 40px;
	text-align: left;
	padding-left: 10px;
	border: 1px solid #ccc;
	border-radius: 5px;
	background-color: #f9f9f9;
  }
  
  
  .button-container {
	display: flex;
	justify-content: space-between;
	margin-top: 20px;
  }
  
  .btn {
	width: 48%;
	height: 40px;
	font-size: 16px;
	text-align: center;
	line-height: 40px;
	background-color: #007AFF;
	color: white;
	border: none;
	border-radius: 5px;
  }
  
  .btn-submit {
	background-color: #28a745;
  }
  
	/* 获取手机号按钮容器 */
	.phone-button-container {
	  padding: 20px;
	  text-align: center;
	}
  
	/* 获取手机号按钮样式 */
	.phone-button {
	  width: 100%;
	  height: 50px;
	  line-height: 50px;
	  font-size: 16px;
	  color: #fff;
	  background-color: #007AFF;
	  border: none;
	  border-radius: 10px;
	  box-shadow: 0 4px 6px rgba(0, 122, 255, 0.2);
	  transition: background-color 0.3s ease;
	}
  
	.phone-button:active {
	  background-color: #005bb5;
	}
  </style>
  