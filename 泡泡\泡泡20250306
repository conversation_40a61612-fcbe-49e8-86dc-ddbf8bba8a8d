<template>
  <view class="container">
    <canvas 
      id="bubbleCanvas"
      ref="bubbleCanvas"
      :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
      @touchstart="handleClick"
    ></canvas>
  </view>
</template>

<script>
export default {
  data() {
    return {
      canvasWidth: 300,
      canvasHeight: 500,
      bubbles: [],
      ctx: null,
      animationFrameId: null
    };
  },
  mounted() {
    this.initCanvas();
    this.createBubbles(15);
    this.animate();
  },
  beforeDestroy() {
    cancelAnimationFrame(this.animationFrameId);
  },
  methods: {
    initCanvas() {
      const canvas = this.$refs.bubbleCanvas;
      if (!canvas) return;

      const systemInfo = uni.getSystemInfoSync();
      this.canvasWidth = systemInfo.windowWidth;
      this.canvasHeight = systemInfo.windowHeight;

      // 设置 Canvas 实际尺寸
      canvas.width = this.canvasWidth;
      canvas.height = this.canvasHeight;

      // 获取原生 Canvas 上下文
      this.ctx = canvas.getContext('2d');
      if (!this.ctx) {
        console.error('无法获取Canvas上下文');
        return;
      }
    },

    createBubbles(count) {
      for (let i = 0; i < count; i++) {
        this.bubbles.push({
          x: Math.random() * this.canvasWidth,
          y: Math.random() * this.canvasHeight,
          radius: Math.random() * 20 + 10,
          dx: (Math.random() - 0.5) * 3,
          dy: (Math.random() - 0.5) * 3,
          color: this.generateBubbleColor(),
          alpha: Math.random() * 0.4 + 0.3,
          targetRadius: Math.random() * 15 + 15
        });
      }
    },

    generateBubbleColor() {
      const hue = Math.floor(Math.random() * 360);
      return `hsla(${hue}, 70%, 60%, `;
    },

    drawBubble(bubble) {
      const gradient = this.ctx.createRadialGradient(
        bubble.x, bubble.y, 0,
        bubble.x, bubble.y, bubble.radius
      );
      
      gradient.addColorStop(0, `${bubble.color}${bubble.alpha + 0.2})`);
      gradient.addColorStop(0.7, `${bubble.color}${bubble.alpha})`);
      gradient.addColorStop(1, `${bubble.color}${bubble.alpha - 0.2})`);

      this.ctx.beginPath();
      this.ctx.arc(bubble.x, bubble.y, bubble.radius, 0, Math.PI * 2);
      this.ctx.fillStyle = gradient;
      this.ctx.shadowColor = `hsla(0, 0%, 100%, ${bubble.alpha * 0.5})`;
      this.ctx.shadowBlur = bubble.radius * 1.5;
      this.ctx.fill();

      // 绘制高光
      this.ctx.beginPath();
      this.ctx.arc(
        bubble.x - bubble.radius * 0.3,
        bubble.y - bubble.radius * 0.3,
        bubble.radius * 0.2,
        0,
        Math.PI * 2
      );
      this.ctx.fillStyle = `hsla(0, 0%, 100%, ${bubble.alpha + 0.1})`;
      this.ctx.fill();
    },

    animate() {
      if (!this.ctx) return;

      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      this.bubbles.forEach(bubble => this.drawBubble(bubble));
      this.updateBubbles();
      this.animationFrameId = requestAnimationFrame(this.animate.bind(this));
    },

    handleClick(e) {
      const canvas = this.$refs.bubbleCanvas;
      if (!canvas) return;

      const rect = canvas.getBoundingClientRect();
      const x = (e.touches[0].clientX - rect.left) * (this.canvasWidth / rect.width);
      const y = (e.touches[0].clientY - rect.top) * (this.canvasHeight / rect.height);

      this.bubbles.forEach(bubble => {
        const dx = x - bubble.x;
        const dy = y - bubble.y;
        if (Math.sqrt(dx * dx + dy * dy) < bubble.radius) {
          console.log('点击泡泡:', bubble);
          bubble.color = `hsla(${Math.random() * 360}, 70%, 60%, `;
        }
      });
    }
  }
};
</script>

<style>
.container {
  background: linear-gradient(135deg, #1a0f33 0%, #3b2a5e 100%);
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

canvas {
  background: transparent;
}
</style>