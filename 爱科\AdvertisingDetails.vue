<template>
  <view>
    <!-- 广告内容展示区 -->
    <view class="advertisement-section">
      <view class="advertisement-title">
		  <text class="title">{{ advertisement.title }}</text>
	  </view>
      <view class="advertisement-body">
        <view class="advertisement-images">
		  <text class="advertisement-time">发布时间：{{ formatTime(advertisement.createdAt) }}</text>
          <image :src="advertisement.image" class="advertisement-image"></image>
        </view>
		<view class="advertisement-description" v-html="advertisement.description"></view>
      </view>
    </view>
  </view>
</template>

<script>
import { generateSignature } from '@/utils/auth'

export default {
    data() {
        return {
            swiperlist: [],
            advertisement: {
                title: "",
                description: "",
                images: "",
                createdAt: ""
            },
            isLoggedIn: false,
            username: "",
            openid: ""
        };
    },
    onLoad(options) {
        // 从路由参数获取广告ID
        this.adId = options.adId || 1 // 默认显示ID为1的广告
        this.fetchAdvertisement()
    },
    methods: {
        formatTime(timestamp) {
          if (!timestamp) return '未知时间'
          const date = new Date(timestamp)
          return `${date.getFullYear()}-${date.getMonth()+1}-${date.getDate()}`
        },
        // 获取广告详情
        async fetchAdvertisement() {
            try {
                const timestamp = Math.floor(Date.now() / 1000)
                const nonce = Math.random().toString(36).substring(2, 10)
                const signature = generateSignature(timestamp, nonce)
                
                const res = await uni.request({
                    url: 'https://aikexiaozhan.com/api/getAdvertisement',
                    method: 'POST',
                    data: { adId: this.adId },
                    header: {
                        'X-Timestamp': timestamp,
                        'X-Nonce': nonce,
                        'X-Signature': signature,
                        'Content-Type': 'application/json'
                    }
                })
                
                if (res.statusCode === 200) {
                    this.advertisement = res.data.data
                    // 记录广告浏览
                }
            } catch (error) {
                console.error('获取广告详情失败:', error)
            }
        },
  },
};
</script>

<style scoped>	
/* 广告内容展示区样式 */
.advertisement-section {
  padding: 20px;
}

.advertisement-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.advertisement-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

.advertisement-images {
  display: block;
  flex-wrap: wrap;
  gap: 10px;
}

.advertisement-image {
  width: 100%;
  max-width: 750rpx; /* 使用响应式单位 */
  height: 400rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.advertisement-time {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
}
.advertisement-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.advertisement-description >>> p {
  margin-bottom: 10px;
}

.advertisement-description >>> a {
  color: #007AFF;
  text-decoration: none;
}

.advertisement-description >>> img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 10px 0;
}
</style>
