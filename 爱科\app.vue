<script>
import { generateSignature } from '@/utils/auth'
export default {
  onLaunch: function() {
    console.log('App Launch')
    this.getOpenId()
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  },
  methods: {
    async getOpenId() {
      // 检查是否已经保存过 openid
      const savedOpenId = uni.getStorageSync('openid')
      if (savedOpenId) {
        console.log('openid 已存在:', savedOpenId)
        await this.checkLoginStatus(savedOpenId)
        return
      }
      try {
        // 1. 获取微信code
        const loginRes = await uni.login({
          provider: 'weixin'
        });
        if (loginRes.errMsg !== 'login:ok') {
          throw new Error('微信登录失败');
        }

        // 2. 发送code到后端
        const apiRes = await uni.request({
          url: 'https://aikexiaozhan.com:9090/api/get-openid',
          method: 'POST',
          data: { code: loginRes.code },
          header: { 'Content-Type': 'application/json' }
        });

        if (apiRes.statusCode !== 200 || apiRes.data.code !== 200) {
          throw new Error(apiRes.data.message || '获取openid失败');
        }

        // 3. 存储openid
        const openid = apiRes.data.openid;
        uni.setStorageSync('openid', openid);
        console.log('获取的openid:', openid);

        // 4. 检查登录状态
        await this.checkLoginStatus(openid)

      } catch (error) {
        console.error('获取openid失败:', error);
        // 可设置重试逻辑
      }
    },
    async checkLoginStatus(openid) {
      try {
        const timestamp = Math.floor(Date.now() / 1000)
        const nonce = Math.random().toString(36).substring(2, 10)
        const signature = generateSignature(timestamp, nonce)
        const res = await uni.request({
          url: 'https://aikexiaozhan.com:9090/api/checkloginstatus',
          method: 'POST',
          data: { 'openid': openid },
          header: {
            'X-Timestamp': timestamp,
            'X-Nonce': nonce,
            'X-Signature': signature,
            'Content-Type': 'application/json'
          }
        });

        if (res.statusCode === 200 && res.data.code === 200) {
          // 标记为已登录状态
		  uni.setStorageSync('userInfo', {
		    parentName: res.data.parentName,
		    contactNumber: res.data.contactNumber,
		    isLoggedIn: true,
			store: res.data.storeInfo || {}  // 新增门店信息存储
		  });
          console.log('用户已登录，用户信息已保存')
        } else {
          // 标记为未登录状态
          uni.setStorageSync('isLoggedIn', false)
          console.log('用户未登录')
        }
      } catch (error) {
        console.error('检查登录状态失败:', error)
      }
    }
  }
}
</script>