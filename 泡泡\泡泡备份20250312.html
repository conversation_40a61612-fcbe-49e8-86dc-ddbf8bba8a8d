<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
<title>Bubbles</title>
<style>
html, body {
    border: none;
    overflow: hidden;
    height: 100%;
    margin: 0;
    padding: 0;
}

body { 
    background: url(BG.jpg) bottom; 
    position: relative;
    display: flex;
    flex-direction: row; /* 让区域水平排列 */
}

/* 5 个水平排列的区域 */
.region {
    height: 100%; /* 区域占满整个高度 */
    border-left: 2px solid rgba(255, 255, 255, 0.8); /* 竖向分割线 */
    position: relative;
}

/* 颜色区分每个区域 */
.region:nth-child(1) { background: rgba(255, 0, 0, 0.2); width: 10%; }  /* 红色 10% */
.region:nth-child(2) { background: rgba(0, 255, 0, 0.2); width: 18%; }  /* 绿色 18% */
.region:nth-child(3) { background: rgba(0, 0, 255, 0.2); width: 24%; }  /* 蓝色 24% */
.region:nth-child(4) { background: rgba(255, 255, 0, 0.2); width: 24%; }  /* 黄色 24% */
.region:nth-child(5) { background: rgba(255, 0, 255, 0.2); width: 24%; }  /* 紫色 24% */

.popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 2px solid black;
    padding: 20px;
    z-index: 1000;
}

.popup img {
    max-width: 100px;
    max-height: 100px;
}
</style>
</head>

<body onload="Demo()">
    <!-- 5 个水平排列的区域 -->
    <div class="region"></div>
    <div class="region"></div>
    <div class="region"></div>
    <div class="region"></div>
    <div class="region"></div>

    <div class="popup" id="bubblePopup">
        <h2>泡泡信息</h2>
        <p id="bubbleId"></p>
        <img id="bubbleImage" src="" alt="Bubble Image">
        <button id="closeButton">关闭</button>
    </div>

<script>
// 定义每个区域目标泡泡数及当前计数
var bubbleTarget = [26, 46, 61, 58, 60]; 
var bubbleCount = [0, 0, 0, 0, 0];
var bubbleIdCounter = 1; // 唯一ID计数器
var currentRegionIndex = 0; // 当前生成泡泡的区域索引

function Demo() {
    if (currentRegionIndex < bubbleTarget.length) {
        if (bubbleCount[currentRegionIndex] < bubbleTarget[currentRegionIndex]) {
            CreateBubble(currentRegionIndex);
            bubbleCount[currentRegionIndex]++;
        }
        // 如果当前区域未达到目标泡泡数，则继续调用 Demo
        if (bubbleCount[currentRegionIndex] < bubbleTarget[currentRegionIndex]) {
            setTimeout(Demo, 1000);
        } else {
            // 移动到下一个区域
            currentRegionIndex++;
            setTimeout(Demo, 1000); // 等待后再生成下一个区域的泡泡
        }
    }
}

+function() {
    var _VER_ = navigator.userAgent;
    var _IE6_ = /IE 6/.test(_VER_);
    var STD = !!window.addEventListener;
    var de = document.documentElement;

    _IE6_ && document.execCommand("BackgroundImageCache", false, true);

    var K = 0.999; // 速度衰减系数
    var POW_RATE = 0.0001; // 随机速度变化概率
    var POW_RANGE = 0.8; // 随机速度变化范围

    // 随机生成初始速度
    function SPEED_X() { return 8 + RND() * 4; }
    function SPEED_Y() { return 6 + RND() * 2; }

    var arrBubs = []; // 存储所有泡泡实例
    var iBottom; // 窗口底部边界
    var iRight;  // 窗口右侧边界

    // 数学函数简写
    var SQRT = Math.sqrt;
    var ATAN2 = Math.atan2;
    var SIN = Math.sin;
    var COS = Math.cos;
    var ABS = Math.abs;
    var RND = Math.random;
    var ROUND = Math.round;

    // 定时器函数，每隔指定时间调用回调
    function Timer(call, time) {
        var last = +new Date;
        var delay = 0;
        return setInterval(function() {
            var cur = +new Date;
            delay += (cur - last);
            last = cur;
            if (delay >= time) {
                call();
                delay %= time;
            }
        }, 1);
    }

    // 每 17ms 更新一次动画
    Timer(update, 17);

    // 创建新泡泡
	function CreateBubble(regionIndex) {
		var regions = document.querySelectorAll(".region");
		var region = regions[regionIndex]; // 获取当前区域
		var regionRect = region.getBoundingClientRect(); // 获取区域的边界

		var bub = new Bubble(10, bubbleIdCounter++); // 初始直径，分配唯一ID
		var startX = regionRect.left + Math.random() * (regionRect.width - 10);
		var startY = regionRect.top + Math.random() * (regionRect.height - 10);

		bub.setX(startX);
		bub.setY(startY);
		bub.vx = SPEED_X() * (Math.random() > 0.5 ? 1 : -1); // 让泡泡水平速度随机正负
		bub.vy = SPEED_Y() * (Math.random() > 0.5 ? 1 : -1);
		bub.regionBounds = regionRect; // 存储该泡泡的活动边界

		arrBubs.push(bub);
		bub.oBox.onclick = function() { showPopup(bub.id); }; // 添加点击事件
	}

	window.CreateBubble = CreateBubble; // 让它成为全局函数
	
    // 更新动画帧
    function update() {
        var n = arrBubs.length;
        var bub, bub2;

        updateWall(); // 更新窗口边界

        for (var i = 0; i < n; i++) {
            bub = arrBubs[i];
            bub.paint(); // 更新泡泡透明度
            bub.vx *= K; // 速度衰减
            bub.vy *= K;

            // 随机触发大小变化（1% 概率）
            if (RND() < 0.01) {
                var newTargetD = 10 + RND() * 80; // 新目标大小
                bub.setTargetSize(newTargetD);
            }

            // 实现大小渐变
            if (bub.D !== bub.targetD) {
                var delta = bub.targetD - bub.D;
                if (Math.abs(delta) < bub.sizeSpeed) {
                    bub.D = bub.targetD;
                } else {
                    bub.D += delta > 0 ? bub.sizeSpeed : -bub.sizeSpeed;
                }
                bub.updateSize(); // 更新显示大小
            }

            // 随机调整速度
            if (RND() < POW_RATE) {
                bub.vx = SPEED_X() * (1 + RND() * POW_RANGE);
                bub.vy = SPEED_Y() * (1 + RND() * POW_RANGE);
            }

            // 更新位置
            bub.setX(bub.x + bub.vx);
            bub.setY(bub.y + bub.vy);
            checkWalls(bub); // 检查墙壁碰撞
        }

        // 检查泡泡间的碰撞
        for (var i = 0; i < n - 1; i++) {
            bub = arrBubs[i];
            for (var j = i + 1; j < n; j++) {
                bub2 = arrBubs[j];
                checkCollision(bub, bub2);
            }
        }
    }

    // 更新窗口边界
    function updateWall() {
        iRight = de.clientWidth;
        iBottom = de.clientHeight;
    }

	// 修改 checkWalls 让泡泡在自己的区域内反弹
	function checkWalls(bub) {
		var bounds = bub.regionBounds;

		if (bub.x < bounds.left) {
			bub.setX(bounds.left);
			bub.vx *= -1;
		} else if (bub.x > bounds.right - bub.D) {
			bub.setX(bounds.right - bub.D);
			bub.vx *= -1;
		}

		if (bub.y < bounds.top) {
			bub.setY(bounds.top);
			bub.vy *= -1;
		} else if (bub.y > bounds.bottom - bub.D) {
			bub.setY(bounds.bottom - bub.D);
			bub.vy *= -1;
		}
	}

    // 向量旋转计算
    function rotate(x, y, sin, cos, reverse) {
        if (reverse)
            return { x: x * cos + y * sin, y: y * cos - x * sin };
        else
            return { x: x * cos - y * sin, y: y * cos + x * sin };
    }

    // 检查并处理泡泡碰撞
    function checkCollision(bub0, bub1) {
        var dx = bub1.x - bub0.x;
        var dy = bub1.y - bub0.y;
        var dist = SQRT(dx * dx + dy * dy);
        var minDist = (bub0.D + bub1.D) / 2; // 使用平均直径判断碰撞

        if (dist < minDist) {
            var angle = ATAN2(dy, dx);
            var sin = SIN(angle);
            var cos = COS(angle);

            var pos0 = { x: 0, y: 0 };
            var pos1 = rotate(dx, dy, sin, cos, true);

            var vel0 = rotate(bub0.vx, bub0.vy, sin, cos, true);
            var vel1 = rotate(bub1.vx, bub1.vy, sin, cos, true);

            var vxTotal = vel0.x - vel1.x;
            vel0.x = vel1.x;
            vel1.x = vxTotal + vel0.x;

            var absV = ABS(vel0.x) + ABS(vel1.x);
            var overlap = minDist - ABS(pos0.x - pos1.x);

            pos0.x += vel0.x / absV * overlap;
            pos1.x += vel1.x / absV * overlap;

            var pos0F = rotate(pos0.x, pos0.y, sin, cos, false);
            var pos1F = rotate(pos1.x, pos1.y, sin, cos, false);

            bub1.setX(bub0.x + pos1F.x);
            bub1.setY(bub0.y + pos1F.y);
            bub0.setX(bub0.x + pos0F.x);
            bub0.setY(bub0.y + pos0F.y);

            var vel0F = rotate(vel0.x, vel0.y, sin, cos, false);
            var vel1F = rotate(vel1.x, vel1.y, sin, cos, false);

            bub0.vx = vel0F.x;
            bub0.vy = vel0F.y;
            bub1.vx = vel1F.x;
            bub1.vy = vel1F.y;
        }
    }

    var APLHA = 0.8; // 透明度衰减系数
    var POW = [1, APLHA, APLHA * APLHA]; // 透明度层级

    // 泡泡类定义
    function Bubble(initialD, id) {
        this.D = initialD;        // 当前直径
        this.targetD = initialD;  // 目标直径
        this.sizeSpeed = 2;       // 渐变速度（每帧变化像素数）
		this.id = id;    
        var kOpa = [], kStp = [];
        var arrFlt = [];
        var oBox = document.body.appendChild(document.createElement("div"));

        styBox = oBox.style;
        styBox.position = "absolute";
        styBox.width = this.D + "px";
        styBox.height = this.D + "px";

        // 创建泡泡的子元素
        for (var i = 0; i < 4; i++) {
            var div = document.createElement("div");
            var sty = div.style;

            sty.position = "absolute";
            sty.width = this.D + "px";
            sty.height = this.D + "px";

            oBox.appendChild(div);

            if (i == 3) { // 最后一层为心形图片
                if (_IE6_)
                    sty.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(src=heart.png)";
                else
                    sty.backgroundImage = "url(heart.png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                break;
            }

            kOpa[i] = 3 * RND(); // 随机透明度初始值
            kStp[i] = 0.02 * RND(); // 随机透明度变化步长

            if (STD) {
                sty.backgroundImage = "url(ch" + i + ".png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                arrFlt[i] = sty;
            } else {
                sty.filter = "alpha progid:DXImageTransform.Microsoft.AlphaImageLoader(src=ch" + i + ".png)";
                arrFlt[i] = div.filters.alpha;
            }
        }

        this.styBox = styBox;
        this.oBox = oBox; // 保存容器以便更新子元素
        this.kOpa = kOpa;
        this.kStp = kStp;
        this.arrFlt = arrFlt;
    }

    // 设置泡泡 X 坐标
    Bubble.prototype.setX = function(x) {
        this.x = x;
        this.styBox.left = ROUND(x) + "px";
    };

    // 设置泡泡 Y 坐标
    Bubble.prototype.setY = function(y) {
        this.y = y;
        this.styBox.top = ROUND(y) + "px";
    };

    // 设置目标大小
    Bubble.prototype.setTargetSize = function(newTargetD) {
        this.targetD = newTargetD;
    };

    // 更新泡泡显示大小
    Bubble.prototype.updateSize = function() {
        this.styBox.width = this.D + "px";
        this.styBox.height = this.D + "px";
        for (var i = 0; i < this.oBox.children.length; i++) {
            var div = this.oBox.children[i];
            div.style.width = this.D + "px";
            div.style.height = this.D + "px";
            div.style.backgroundSize = this.D + "px " + this.D + "px";
        }
    };

    // 更新泡泡透明度动画
    Bubble.prototype.paint = function() {
        var i, v;
        for (i = 0; i < 3; i++) {
            v = ABS(SIN(this.kOpa[i] += this.kStp[i] * RND()));
            v *= POW[i];
            v = ((v * 1e4) >> 0) / 1e4;
            this.arrFlt[i].opacity = STD ? v : v * 100;
        }
    };

    // 显示泡泡信息弹窗
    function showPopup(bubbleId) {
        document.getElementById('bubbleId').innerText = "泡泡 ID: " + bubbleId;
        document.getElementById('bubbleImage').src = "img/bubble_" + bubbleId + ".png"; // 假设图片命名为 bubble_0.png, bubble_1.png, ...
        document.getElementById('bubblePopup').style.display = 'block';
    }

    // 关闭弹窗
    function closePopup() {
        document.getElementById('bubblePopup').style.display = 'none';
    }

    // 绑定关闭按钮事件
    document.getElementById('closeButton').onclick = closePopup;

}();
</script>
</body>
</html>