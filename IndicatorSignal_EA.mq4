//+------------------------------------------------------------------+
//|                                           IndicatorSignal_EA.mq4 |
//|                                    基于指标信号的智能交易系统    |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Indicator Signal Trading System"
#property version   "1.00"
#property strict

//--- 输入参数
input group "=== 指标设置 ==="
input string Indicator_Name = "";           // 指标文件名(留空则使用当前图表指标)
input int    Indicator_Handle = 0;          // 指标句柄(高级用户)

input group "=== 交易设置 ==="
input double Lot_Size = 0.1;               // 固定手数(0=自动计算)
input double Risk_Percent = 2.0;           // 风险百分比(自动手数时使用)
input int    Magic_Number = 20241201;      // EA魔术数字
input int    Slippage = 3;                 // 滑点容忍度

input group "=== 止损止盈 ==="
input int    Stop_Loss_Points = 500;       // 止损点数(0=不设置)
input int    Take_Profit_Points = 1000;    // 止盈点数(0=不设置)
input bool   Use_ATR_SL = true;            // 使用ATR动态止损
input int    ATR_Period = 14;              // ATR周期
input double ATR_Multiplier = 2.0;         // ATR倍数

input group "=== 风险控制 ==="
input double Max_Daily_Loss = 5.0;         // 最大日亏损百分比
input double Max_Spread = 5.0;             // 最大允许点差
input int    Max_Orders = 1;               // 最大同时订单数
input bool   One_Direction_Only = true;    // 仅持有单一方向订单

input group "=== 时间过滤 ==="
input bool   Use_Time_Filter = false;      // 使用时间过滤
input int    Start_Hour = 8;               // 开始交易时间
input int    End_Hour = 22;                // 结束交易时间
input bool   Avoid_News_Time = true;       // 避开新闻时间

input group "=== 其他设置 ==="
input bool   Show_Info_Panel = true;       // 显示信息面板
input bool   Send_Notifications = true;    // 发送通知
input bool   Write_Log = true;             // 写入日志

//--- 全局变量
double g_lastBuySignal = EMPTY_VALUE;      // 上次买入信号值
double g_lastSellSignal = EMPTY_VALUE;     // 上次卖出信号值
double g_lastCloseSellSignal = EMPTY_VALUE; // 上次平空信号值(序号4)
double g_lastCloseBuySignal = EMPTY_VALUE;  // 上次平多信号值(序号5)
double g_dailyStartBalance = 0;            // 当日起始余额
datetime g_lastBarTime = 0;                // 上一根K线时间
int g_totalOrders = 0;                     // 当前订单总数

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化
    g_dailyStartBalance = AccountBalance();
    g_lastBarTime = Time[0];
    
    // 验证参数
    if(!ValidateInputs())
    {
        Print("参数验证失败，EA停止运行");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    // 初始化信号值
    InitializeSignals();
    
    Print("指标信号EA初始化成功");
    Print("账户余额: ", DoubleToString(AccountBalance(), 2));
    Print("风险设置: ", DoubleToString(Risk_Percent, 1), "%");
    
    if(Show_Info_Panel)
    {
        CreateInfoPanel();
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(Show_Info_Panel)
    {
        DeleteInfoPanel();
    }
    
    Print("指标信号EA停止运行，原因: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查新K线
    if(!IsNewBar()) return;
    
    // 更新订单计数
    UpdateOrderCount();
    
    // 检查日亏损熔断
    if(CheckDailyLossLimit())
    {
        if(Write_Log) Print("触发日亏损熔断，停止交易");
        return;
    }
    
    // 检查点差过滤
    if(!CheckSpreadFilter())
    {
        return;
    }
    
    // 检查时间过滤
    if(!CheckTimeFilter())
    {
        return;
    }
    
    // 检查平仓信号
    CheckCloseSignals();
    
    // 检查开仓信号
    CheckOpenSignals();
    
    // 更新信息面板
    if(Show_Info_Panel)
    {
        UpdateInfoPanel();
    }
}

//+------------------------------------------------------------------+
//| 验证输入参数                                                     |
//+------------------------------------------------------------------+
bool ValidateInputs()
{
    if(Risk_Percent <= 0 || Risk_Percent > 20)
    {
        Print("错误: 风险百分比必须在0-20%之间");
        return false;
    }
    
    if(Max_Daily_Loss <= 0 || Max_Daily_Loss > 50)
    {
        Print("错误: 最大日亏损必须在0-50%之间");
        return false;
    }
    
    if(Lot_Size < 0)
    {
        Print("错误: 手数不能为负数");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 初始化信号值                                                     |
//+------------------------------------------------------------------+
void InitializeSignals()
{
    // 获取当前指标值作为初始值
    g_lastBuySignal = GetIndicatorValue(0, 1);
    g_lastSellSignal = GetIndicatorValue(1, 1);
    g_lastCloseSellSignal = GetIndicatorValue(4, 1);  // 平空信号
    g_lastCloseBuySignal = GetIndicatorValue(5, 1);   // 平多信号
}

//+------------------------------------------------------------------+
//| 获取指标值                                                       |
//+------------------------------------------------------------------+
double GetIndicatorValue(int buffer, int shift)
{
    if(StringLen(Indicator_Name) > 0)
    {
        // 使用指定的指标文件
        return iCustom(Symbol(), 0, Indicator_Name, buffer, shift);
    }
    else
    {
        // 使用图表上的第一个指标窗口
        return iCustom(Symbol(), 0, "", buffer, shift);
    }
}

//+------------------------------------------------------------------+
//| 检查是否为新K线                                                 |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = Time[0];
    if(currentBarTime != g_lastBarTime)
    {
        g_lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 更新订单计数                                                     |
//+------------------------------------------------------------------+
void UpdateOrderCount()
{
    g_totalOrders = 0;
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == Magic_Number)
            {
                g_totalOrders++;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查日亏损限制                                                   |
//+------------------------------------------------------------------+
bool CheckDailyLossLimit()
{
    static int lastDay = -1;
    int currentDay = TimeDay(TimeCurrent());
    
    if(currentDay != lastDay)
    {
        g_dailyStartBalance = AccountBalance();
        lastDay = currentDay;
        return false;
    }
    
    double currentBalance = AccountBalance();
    double dailyLoss = (g_dailyStartBalance - currentBalance) / g_dailyStartBalance * 100;
    
    return (dailyLoss >= Max_Daily_Loss);
}

//+------------------------------------------------------------------+
//| 检查点差过滤                                                     |
//+------------------------------------------------------------------+
bool CheckSpreadFilter()
{
    double currentSpread = (Ask - Bid) / Point;
    return (currentSpread <= Max_Spread);
}

//+------------------------------------------------------------------+
//| 检查时间过滤                                                     |
//+------------------------------------------------------------------+
bool CheckTimeFilter()
{
    if(!Use_Time_Filter) return true;
    
    int currentHour = TimeHour(TimeCurrent());
    return (currentHour >= Start_Hour && currentHour <= End_Hour);
}

//+------------------------------------------------------------------+
//| 检查平仓信号                                                     |
//+------------------------------------------------------------------+
void CheckCloseSignals()
{
    // 检查平空信号(序号4)
    double currentCloseSellSignal = GetIndicatorValue(4, 0);
    bool closeSellTriggered = false;

    if(g_lastCloseSellSignal == EMPTY_VALUE && currentCloseSellSignal != EMPTY_VALUE)
    {
        closeSellTriggered = true;
    }

    if(closeSellTriggered)
    {
        CloseSellOrders();
        if(Write_Log) Print("平空信号触发，关闭所有卖单");
        if(Send_Notifications) SendNotification("平空信号触发");
    }

    // 检查平多信号(序号5)
    double currentCloseBuySignal = GetIndicatorValue(5, 0);
    bool closeBuyTriggered = false;

    if(g_lastCloseBuySignal == EMPTY_VALUE && currentCloseBuySignal != EMPTY_VALUE)
    {
        closeBuyTriggered = true;
    }

    if(closeBuyTriggered)
    {
        CloseBuyOrders();
        if(Write_Log) Print("平多信号触发，关闭所有买单");
        if(Send_Notifications) SendNotification("平多信号触发");
    }

    // 更新信号值
    g_lastCloseSellSignal = currentCloseSellSignal;
    g_lastCloseBuySignal = currentCloseBuySignal;
}

//+------------------------------------------------------------------+
//| 检查开仓信号                                                     |
//+------------------------------------------------------------------+
void CheckOpenSignals()
{
    // 如果已达到最大订单数，不开新单
    if(g_totalOrders >= Max_Orders) return;

    double currentBuySignal = GetIndicatorValue(0, 0);
    double currentSellSignal = GetIndicatorValue(1, 0);

    // 检查买入信号
    bool buySignalTriggered = false;
    if(g_lastBuySignal == EMPTY_VALUE && currentBuySignal != EMPTY_VALUE)
    {
        buySignalTriggered = true;
    }

    // 检查卖出信号
    bool sellSignalTriggered = false;
    if(g_lastSellSignal == EMPTY_VALUE && currentSellSignal != EMPTY_VALUE)
    {
        sellSignalTriggered = true;
    }

    // 单一方向检查
    if(One_Direction_Only)
    {
        int currentDirection = GetCurrentDirection();

        if(buySignalTriggered && currentDirection == -1)
        {
            // 有卖单时不能开买单
            buySignalTriggered = false;
        }

        if(sellSignalTriggered && currentDirection == 1)
        {
            // 有买单时不能开卖单
            sellSignalTriggered = false;
        }
    }

    // 执行交易
    if(buySignalTriggered)
    {
        OpenTrade(OP_BUY);
        if(Write_Log) Print("买入信号触发，开买单");
        if(Send_Notifications) SendNotification("买入信号触发");
    }

    if(sellSignalTriggered)
    {
        OpenTrade(OP_SELL);
        if(Write_Log) Print("卖出信号触发，开卖单");
        if(Send_Notifications) SendNotification("卖出信号触发");
    }

    // 更新信号值
    g_lastBuySignal = currentBuySignal;
    g_lastSellSignal = currentSellSignal;
}

//+------------------------------------------------------------------+
//| 获取当前持仓方向                                                 |
//+------------------------------------------------------------------+
int GetCurrentDirection()
{
    int buyOrders = 0;
    int sellOrders = 0;

    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == Magic_Number)
            {
                if(OrderType() == OP_BUY) buyOrders++;
                if(OrderType() == OP_SELL) sellOrders++;
            }
        }
    }

    if(buyOrders > 0 && sellOrders == 0) return 1;   // 只有买单
    if(sellOrders > 0 && buyOrders == 0) return -1;  // 只有卖单
    return 0; // 无持仓或双向持仓
}

//+------------------------------------------------------------------+
//| 关闭买单(平多)                                                   |
//+------------------------------------------------------------------+
void CloseBuyOrders()
{
    for(int i = OrdersTotal() - 1; i >= 0; i--)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == Magic_Number && OrderType() == OP_BUY)
            {
                bool result = OrderClose(OrderTicket(), OrderLots(), Bid, Slippage, clrRed);

                if(result)
                {
                    if(Write_Log) Print("买单关闭成功: ", OrderTicket());
                }
                else
                {
                    if(Write_Log) Print("买单关闭失败: ", OrderTicket(), " 错误: ", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 关闭卖单(平空)                                                   |
//+------------------------------------------------------------------+
void CloseSellOrders()
{
    for(int i = OrdersTotal() - 1; i >= 0; i--)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == Magic_Number && OrderType() == OP_SELL)
            {
                bool result = OrderClose(OrderTicket(), OrderLots(), Ask, Slippage, clrBlue);

                if(result)
                {
                    if(Write_Log) Print("卖单关闭成功: ", OrderTicket());
                }
                else
                {
                    if(Write_Log) Print("卖单关闭失败: ", OrderTicket(), " 错误: ", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 关闭所有订单                                                     |
//+------------------------------------------------------------------+
void CloseAllOrders()
{
    CloseBuyOrders();
    CloseSellOrders();
}

//+------------------------------------------------------------------+
//| 开仓交易                                                         |
//+------------------------------------------------------------------+
void OpenTrade(int orderType)
{
    double lotSize = CalculateLotSize();
    if(lotSize <= 0)
    {
        if(Write_Log) Print("手数计算错误，取消交易");
        return;
    }

    double price, sl, tp;
    string comment = "IndicatorSignal_" + TimeToString(TimeCurrent(), TIME_SECONDS);
    color orderColor = (orderType == OP_BUY) ? clrBlue : clrRed;

    if(orderType == OP_BUY)
    {
        price = Ask;
        sl = CalculateStopLoss(OP_BUY, price);
        tp = CalculateTakeProfit(OP_BUY, price, sl);
    }
    else if(orderType == OP_SELL)
    {
        price = Bid;
        sl = CalculateStopLoss(OP_SELL, price);
        tp = CalculateTakeProfit(OP_SELL, price, sl);
    }
    else
    {
        return;
    }

    int ticket = OrderSend(Symbol(), orderType, lotSize, price, Slippage, sl, tp, comment, Magic_Number, 0, orderColor);

    if(ticket > 0)
    {
        string orderTypeStr = (orderType == OP_BUY) ? "买入" : "卖出";
        if(Write_Log)
        {
            Print(orderTypeStr, "订单成功: 票号=", ticket, " 手数=", DoubleToString(lotSize, 2),
                  " 价格=", DoubleToString(price, Digits), " 止损=", DoubleToString(sl, Digits),
                  " 止盈=", DoubleToString(tp, Digits));
        }
    }
    else
    {
        string orderTypeStr = (orderType == OP_BUY) ? "买入" : "卖出";
        if(Write_Log) Print(orderTypeStr, "订单失败，错误代码: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 计算手数                                                         |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    if(Lot_Size > 0)
    {
        // 使用固定手数
        return NormalizeLots(Lot_Size);
    }

    // 自动计算手数
    double balance = AccountBalance();
    double riskAmount = balance * Risk_Percent / 100;

    double stopDistance = 0;

    if(Use_ATR_SL)
    {
        double atr = iATR(Symbol(), 0, ATR_Period, 1);
        stopDistance = atr * ATR_Multiplier;
    }
    else if(Stop_Loss_Points > 0)
    {
        stopDistance = Stop_Loss_Points * Point;
    }
    else
    {
        // 默认使用ATR
        double atr = iATR(Symbol(), 0, ATR_Period, 1);
        stopDistance = atr * 2.0;
    }

    if(stopDistance <= 0) return 0;

    // 计算每点价值
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    if(tickValue == 0) tickValue = 1;

    // 计算手数
    double lotSize = riskAmount / (stopDistance / Point * tickValue);

    return NormalizeLots(lotSize);
}

//+------------------------------------------------------------------+
//| 标准化手数                                                       |
//+------------------------------------------------------------------+
double NormalizeLots(double lots)
{
    double minLot = MarketInfo(Symbol(), MODE_MINLOT);
    double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
    double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

    if(lotStep == 0) lotStep = 0.01;

    lots = MathMax(minLot, MathMin(maxLot, MathRound(lots / lotStep) * lotStep));

    return NormalizeDouble(lots, 2);
}

//+------------------------------------------------------------------+
//| 计算止损                                                         |
//+------------------------------------------------------------------+
double CalculateStopLoss(int orderType, double entryPrice)
{
    if(Stop_Loss_Points == 0 && !Use_ATR_SL) return 0;

    double stopDistance = 0;

    if(Use_ATR_SL)
    {
        double atr = iATR(Symbol(), 0, ATR_Period, 1);
        stopDistance = atr * ATR_Multiplier;
    }
    else
    {
        stopDistance = Stop_Loss_Points * Point;
    }

    double sl = 0;
    double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;

    if(orderType == OP_BUY)
    {
        sl = entryPrice - stopDistance;
        if(entryPrice - sl < minDistance)
        {
            sl = entryPrice - minDistance;
        }
    }
    else if(orderType == OP_SELL)
    {
        sl = entryPrice + stopDistance;
        if(sl - entryPrice < minDistance)
        {
            sl = entryPrice + minDistance;
        }
    }

    return NormalizeDouble(sl, Digits);
}

//+------------------------------------------------------------------+
//| 计算止盈                                                         |
//+------------------------------------------------------------------+
double CalculateTakeProfit(int orderType, double entryPrice, double stopLoss)
{
    if(Take_Profit_Points == 0) return 0;

    double profitDistance = Take_Profit_Points * Point;

    // 如果设置了止损，确保风险回报比合理
    if(stopLoss > 0)
    {
        double stopDistance = MathAbs(entryPrice - stopLoss);
        double minProfitDistance = stopDistance * 1.5; // 最小1.5倍风险回报比
        profitDistance = MathMax(profitDistance, minProfitDistance);
    }

    double tp = 0;

    if(orderType == OP_BUY)
    {
        tp = entryPrice + profitDistance;
    }
    else if(orderType == OP_SELL)
    {
        tp = entryPrice - profitDistance;
    }

    return NormalizeDouble(tp, Digits);
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
    string panelName = "IndicatorSignalPanel";

    // 创建主面板
    ObjectCreate(panelName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSet(panelName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSet(panelName, OBJPROP_XDISTANCE, 10);
    ObjectSet(panelName, OBJPROP_YDISTANCE, 30);
    ObjectSet(panelName, OBJPROP_XSIZE, 250);
    ObjectSet(panelName, OBJPROP_YSIZE, 220);
    ObjectSet(panelName, OBJPROP_COLOR, clrDarkBlue);
    ObjectSet(panelName, OBJPROP_BGCOLOR, clrNavy);
    ObjectSet(panelName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSet(panelName, OBJPROP_WIDTH, 1);

    // 创建标题
    string titleName = panelName + "_Title";
    ObjectCreate(titleName, OBJ_LABEL, 0, 0, 0);
    ObjectSet(titleName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSet(titleName, OBJPROP_XDISTANCE, 20);
    ObjectSet(titleName, OBJPROP_YDISTANCE, 40);
    ObjectSetText(titleName, "指标信号EA", 12, "Arial Bold", clrWhite);

    // 创建信息标签
    for(int i = 0; i < 8; i++)
    {
        string labelName = panelName + "_Label" + IntegerToString(i);
        ObjectCreate(labelName, OBJ_LABEL, 0, 0, 0);
        ObjectSet(labelName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSet(labelName, OBJPROP_XDISTANCE, 20);
        ObjectSet(labelName, OBJPROP_YDISTANCE, 65 + i * 18);
        ObjectSetText(labelName, "", 9, "Arial", clrLightGray);
    }
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
    string panelName = "IndicatorSignalPanel";

    // 获取当前信号值
    double buySignal = GetIndicatorValue(0, 0);
    double sellSignal = GetIndicatorValue(1, 0);
    double closeSellSignal = GetIndicatorValue(4, 0);  // 平空信号
    double closeBuySignal = GetIndicatorValue(5, 0);   // 平多信号

    // 获取账户信息
    double balance = AccountBalance();
    double equity = AccountEquity();
    double profit = GetCurrentProfit();

    // 更新标签
    ObjectSetText(panelName + "_Label0", "余额: " + DoubleToString(balance, 2), 9, "Arial", clrLightGray);
    ObjectSetText(panelName + "_Label1", "净值: " + DoubleToString(equity, 2), 9, "Arial", clrLightGray);
    ObjectSetText(panelName + "_Label2", "浮盈: " + DoubleToString(profit, 2), 9, "Arial", (profit >= 0) ? clrLime : clrRed);
    ObjectSetText(panelName + "_Label3", "订单数: " + IntegerToString(g_totalOrders), 9, "Arial", clrLightGray);

    string buyStatus = (buySignal != EMPTY_VALUE) ? "有信号" : "无信号";
    string sellStatus = (sellSignal != EMPTY_VALUE) ? "有信号" : "无信号";
    string closeSellStatus = (closeSellSignal != EMPTY_VALUE) ? "有信号" : "无信号";
    string closeBuyStatus = (closeBuySignal != EMPTY_VALUE) ? "有信号" : "无信号";

    ObjectSetText(panelName + "_Label4", "买入信号: " + buyStatus, 9, "Arial", (buySignal != EMPTY_VALUE) ? clrLime : clrGray);
    ObjectSetText(panelName + "_Label5", "卖出信号: " + sellStatus, 9, "Arial", (sellSignal != EMPTY_VALUE) ? clrRed : clrGray);
    ObjectSetText(panelName + "_Label6", "平空信号: " + closeSellStatus, 9, "Arial", (closeSellSignal != EMPTY_VALUE) ? clrYellow : clrGray);
    ObjectSetText(panelName + "_Label7", "平多信号: " + closeBuyStatus, 9, "Arial", (closeBuySignal != EMPTY_VALUE) ? clrOrange : clrGray);
}

//+------------------------------------------------------------------+
//| 删除信息面板                                                     |
//+------------------------------------------------------------------+
void DeleteInfoPanel()
{
    string panelName = "IndicatorSignalPanel";

    ObjectDelete(panelName);
    ObjectDelete(panelName + "_Title");

    for(int i = 0; i < 8; i++)
    {
        ObjectDelete(panelName + "_Label" + IntegerToString(i));
    }
}

//+------------------------------------------------------------------+
//| 获取当前盈亏                                                     |
//+------------------------------------------------------------------+
double GetCurrentProfit()
{
    double totalProfit = 0;

    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == Magic_Number)
            {
                totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
            }
        }
    }

    return totalProfit;
}

//+------------------------------------------------------------------+
//| 发送通知                                                         |
//+------------------------------------------------------------------+
void SendNotification(string message)
{
    string fullMessage = "指标信号EA - " + Symbol() + ": " + message;

    // 发送手机推送通知
    SendNotification(fullMessage);

    // 发送邮件通知（如果设置了邮件）
    if(TerminalInfoString(TERMINAL_MAIL_ENABLED))
    {
        SendMail("MT4交易通知", fullMessage);
    }
}
