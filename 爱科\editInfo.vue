<template>
  <view>
    <view class="form-container">
      <form @submit="submitForm" report-submit>
        <view class="form-item">
          <text class="form-label">紧急联系电话：</text>
          <input type="number" v-model="formData.emergencyContact" placeholder="请输入紧急联系电话" />
        </view>

        <!-- 动态孩子信息 -->
        <view v-for="(child, index) in formData.children" :key="index" class="child-container" style="margin-bottom: 30px; border: 1px solid #eee; border-radius: 10px; padding: 15px; background: #fff;">
          <view style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <text style="font-size: 18px; font-weight: bold;">孩子 {{index + 1}}</text>
            <text v-if="child.hasCard" style="color: #4CD964; font-size: 14px;">(已绑定会员卡)</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">孩子姓名：</text>
            <input 
              type="text" 
              v-model="child.name" 
              placeholder="请输入孩子姓名"
              :disabled="child.hasCard"
            />
          </view>
    <view class="form-item">
      <text class="form-label">性别：</text>
      <picker mode="selector" :range="genderOptions" @change="handleGenderChange($event, index)">
      <view class="pickergrade">{{ child.gender || '请选择性别' }}</view>
      </picker>
    </view>
          <view class="form-item">
            <text class="form-label">就读学校：</text>
            <input type="text" v-model="child.school" placeholder="请输入就读学校" />
          </view>
          <view class="form-item">
            <text class="form-label">现读年级：</text>
            <picker mode="selector" :range="gradeOptions" @change="handleGradeChange($event, index)">
              <view class="pickergrade">{{ child.grade || '请选择年级' }}</view>
            </picker>
          </view>
        </view>
    <view class="add-child">
      <button @click="addChild">+ 添加孩子</button>
    </view>

        <view class="button-container">
          <button class="btn" @click="goBack">返回</button>
          <button form-type="submit" class="btn btn-submit">提交</button>
        </view>
      </form>
    </view>
  </view>
</template>

<script>
import { generateSignature } from '@/utils/auth'
export default {
  data() {
    return {
      formData: {
        emergencyContact: '',
        children: []
      },
  genderOptions: ['男', '女'],
      gradeOptions: [
        "幼儿园小班", "幼儿园中班", "幼儿园大班", 
        "小学一年级", "小学二年级", "小学三年级", "小学四年级", "小学五年级", "小学六年级",
        "初中一年级", "初中二年级", "初中三年级",
        "高中一年级", "高中二年级", "高中三年级"
      ],
    };
  },
  onLoad() {
    this.loadUserData();
  },
  methods: {
  handleGenderChange(event, index) {
  const selectedGender = this.genderOptions[event.detail.value].trim(); 
  this.$set(this.formData.children[index], 'gender', selectedGender);
  },
    handleGradeChange(event, index) {
      const selectedGrade = this.gradeOptions[event.detail.value];
      this.$set(this.formData.children[index], 'grade', selectedGrade);
    },
  addChild() {
          this.formData.children.push({
              name: '',
              school: '',
              grade: '',
              gender: '',
              hasCard: false  // 新增的孩子默认没有会员卡
          });
      },
    async loadUserData() {
      this.loading = true;
      try {
        const openid = uni.getStorageSync('openid');
        if (!openid) {
          uni.showToast({ title: '请先登录', icon: 'none' });
          return;
        }

        const timestamp = Math.floor(Date.now() / 1000)
        const nonce = Math.random().toString(36).substring(2, 10)
        const signature = generateSignature(timestamp, nonce)
        
        const res = await uni.request({
          url: 'https://aikexiaozhan.com/api/getChildrenInfo',
          method: 'POST',
          data: { openid },
          header: {
            'X-Timestamp': timestamp,
            'X-Nonce': nonce,
            'X-Signature': signature,
            'Content-Type': 'application/json'
          }
        });
    

        if (res.statusCode === 200) {
    console.log(res.data);
          this.formData.children = res.data.children.map(child => ({
              name: child.name,
              school: child.school || '',  // 确保有默认值
      gender: (child.gender || '').trim(),  // 确保有默认值
              grade: child.grade || '',    // 确保有默认值
              hasCard: !!child.cardNumber
          }));
          
          // 获取紧急联系电话
          const userRes = await uni.request({
            url: 'https://aikexiaozhan.com/api/checkloginstatus',
            method: 'POST',
            data: { openid },
            header: {
              'X-Timestamp': timestamp,
              'X-Nonce': nonce,
              'X-Signature': signature,
              'Content-Type': 'application/json'
            }
          });
          
          if (userRes.statusCode === 200) {
            this.formData.emergencyContact = userRes.data.emergencyContact;
          }
        }
      } catch (error) {
        uni.showToast({ title: '加载数据失败', icon: 'none' });
      } finally {
          this.loading = false;
      }
    },
    async submitForm() {
      // 验证必填字段
      if (!this.formData.emergencyContact) {
          uni.showToast({ title: '请填写紧急联系电话', icon: 'none' });
          return;
      }
      
      for (const child of this.formData.children) {
          if (!child.name || !child.school || !child.grade || !child.gender) {
                uni.showToast({ title: '请完善所有孩子信息', icon: 'none' });
                return;
              }
      }
      try {
        const openid = uni.getStorageSync('openid');
        if (!openid) {
          uni.showToast({ title: '请先登录', icon: 'none' });
          return;
        }

        const timestamp = Math.floor(Date.now() / 1000)
        const nonce = Math.random().toString(36).substring(2, 10)
        const signature = generateSignature(timestamp, nonce)
        
        const res = await uni.request({
          url: 'https://aikexiaozhan.com/api/updateUserInfo',
          method: 'POST',
          data: {
            ...this.formData,
            openid
          },
          header: {
            'X-Timestamp': timestamp,
            'X-Nonce': nonce,
            'X-Signature': signature,
            'Content-Type': 'application/json'
          }
        });

        if (res.statusCode === 200) {
          uni.showToast({ title: '更新成功', icon: 'success' });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({ title: '更新失败', icon: 'none' });
        }
      } catch (error) {
        uni.showToast({ title: '更新失败', icon: 'none' });
      }
    },
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style scoped>
/* 复用register.vue中的样式 */
.form-container {
  margin: 0 auto;
  padding: 20px;
}

.form-item {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.form-label {
  margin-bottom: 5px;
  font-size: 16px;
  color: #333;
}

input {
  width: 90%;
  height: 40px;
  padding: 5px 10px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.btn {
  flex: 1;
  margin: 0 10px;
  height: 45px;
  line-height: 45px;
  border-radius: 5px;
  background-color: #007AFF;
  color: white;
}

.btn-submit {
  background-color: #4CD964;
}

.pickergrade {
    width: 90%;
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}
.add-child {
    margin: 20px 0;
    text-align: center;
}

.add-child button {
    background-color: #fff;
    color: #007AFF;
    border: 1px solid #007AFF;
    border-radius: 5px;
    padding: 10px 20px;
}

/* 新增样式 */
.child-container {
  margin-bottom: 30px;
  border: 1px solid #eee;
  border-radius: 10px;
  padding: 15px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

/* 修改原有样式 */
.form-item {
  margin-bottom: 15px;
  padding: 0 10px;
}

.form-label {
  color: #666;
  font-size: 15px;
}
</style>