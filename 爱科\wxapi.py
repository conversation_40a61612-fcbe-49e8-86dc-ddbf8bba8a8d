import os
import sys
from dotenv import load_dotenv
from gevent import pywsgi
from gevent import monkey
from flask_cors import CORS


monkey.patch_all()

# 加载.env文件
load_dotenv()


import requests
import json
import pyodbc

from flask import Flask, jsonify, request, redirect
from multiprocessing import cpu_count, Process
import time
from functools import wraps
import hashlib  # Python内置库，无需额外安装

AppID = "wx833f786f2b234ea5"
AppSecret = "48b55748481fdc8f484b5292e226f399"


def getToken():
    get_token_url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + AppID + "&secret=" + AppSecret
    get_token = requests.get(get_token_url)
    Token = get_token.json()["access_token"]
    return Token


def validate_certificates():
    cert_files = {
        'key': 'key/aikexiaozhan.com.key',
        'cert': 'key/aikexiaozhan.com_public.crt',
        'chain': 'key/aikexiaozhan.com_chain.crt'
    }

    for name, path in cert_files.items():
        if not os.path.exists(path):
            raise FileNotFoundError(f"证书文件不存在: {path}")
        if not os.access(path, os.R_OK):
            raise PermissionError(f"无法读取证书文件: {path}")
    return cert_files

def safesql(a):
    if a is None:
        return ''
    if not isinstance(a, str):
        return str(a)

    # 定义需要替换的字符及其替换值
    replace_map = {
        ' ': '\000',
        "'": "\000",
        '"': '\000',
        '&': '\000',
        '>': '\000',
        '<': '\000',
        '=': '\000'
    }

    # 使用 translate 方法进行批量替换
    return a.translate(str.maketrans(replace_map))

# 安全处理日期字段
def format_date(date_value):
    if date_value:
        try:
            if isinstance(date_value, str):
                return date_value
            return date_value.strftime('%Y-%m-%d')
        except:
            return str(date_value)
    return ''

# 签名验证装饰器
def check_signature(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        # 获取头部参数
        timestamp = request.headers.get('X-Timestamp')
        nonce = request.headers.get('X-Nonce')
        signature = request.headers.get('X-Signature')

        # 参数检查
        if not all([timestamp, nonce, signature]):
            return jsonify({'code': 400, 'message': '缺少签名参数'}), 400

        # 验证时间有效性（5分钟有效期）
        try:
            if abs(int(time.time()) - int(timestamp)) > 300:
                return jsonify({'code': 403, 'message': '请求过期'}), 403
        except ValueError:
            return jsonify({'code': 400, 'message': '时间戳格式错误'}), 400

        # 验证签名
        app_secret = 'c90ab4e33f9592d25877e76330481cf6'  # 需与前端一致
        sign_str = f"{timestamp}{nonce}{app_secret}".encode('utf-8')
        expect_sign = hashlib.sha256(sign_str).hexdigest()

        if not signature == expect_sign:
            return jsonify({'code': 403, 'message': '签名验证失败'}), 403

        return f(*args, **kwargs)

    return decorated


app = Flask(__name__)
CORS(app)  # 允许跨域访问
app.config['JSON_AS_ASCII'] = False

# SQL Server 连接配置
# 从环境变量获取配置
DB_CONFIG = {
    "server": os.getenv('DB_SERVER'),
    "database": os.getenv('DB_NAME'),
    "username": os.getenv('DB_USERNAME'),
    "password": os.getenv('DB_PASSWORD')
}


# 连接 SQL Server
def connect_to_sqlserver():
    try:
        conn = pyodbc.connect(
            f"DRIVER={{SQL Server}};"
            f"SERVER={DB_CONFIG['server']};"
            f"DATABASE={DB_CONFIG['database']};"
            f"UID={DB_CONFIG['username']};"
            f"PWD={DB_CONFIG['password']}"
        )
        print("成功连接到数据库")
        return conn
    except Exception as e:
        print(f"连接数据库失败: {str(e)}")
        return None


@app.before_request
def redirect_to_https():
    if not request.is_secure and request.headers.get('X-Forwarded-Proto', 'http') != 'https':
        url = request.url.replace('http://', 'https://', 1)
        if request.url_root.startswith('http://'):
            return redirect(url, code=301)

@app.errorhandler(405)
def method_not_allowed(e):
    return jsonify({
        'code': 405,
        'message': f'Method not allowed. Supported methods: {e.valid_methods}'
    }), 405

@app.route('/api/get-openid', methods=['POST'])
def get_openid():
    code = request.json.get('code')
    if not code:
        return jsonify({'code': 400, 'message': '缺少code参数'}), 400

    # 向微信服务器请求openid
    try:
        resp = requests.get(
            f'https://api.weixin.qq.com/sns/jscode2session?appid={AppID}'
            f'&secret={AppSecret}&js_code={code}&grant_type=authorization_code',
            timeout=5
        )
        data = resp.json()

        if 'openid' not in data:
            return jsonify({'code': 500, 'message': '微信接口返回异常'}), 500

        # 返回openid给前端（生产环境建议返回token而非直接暴露openid）
        return jsonify({
            'code': 200,
            'openid': data['openid'],
            'session_key': data.get('session_key')  # 可选返回
        })

    except Exception as e:
        return jsonify({'code': 500, 'message': str(e)}), 500


# 获取轮播图数据
@app.route('/api/swiper/<category>', methods=['POST'])
@check_signature
def get_swiper(category):
    conn = None
    try:
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({"error": "Database connection failed"}), 500

        cursor = conn.cursor()
        # 添加有效性过滤和排序
        query = """SELECT AdID, ImageUrl, Title 
                 FROM SwiperAds 
                 WHERE Category = ? AND is_active = 1
                 ORDER BY CreatedAt DESC"""  # 按创建时间倒序排列
        cursor.execute(query, (safesql(category),))
        rows = cursor.fetchall()
        result = [{
            "adId": row[0],
            "image": row[1],
            "title": row[2]
        } for row in rows]
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/getphonenumber', methods=['POST'])
@check_signature
def login():
    # 从 POST 请求的 JSON 数据中获取 code
    code = request.json.get('code')
    if not code:
        return jsonify({"error": "Missing code parameter"}), 400

    try:
        data = {
            "code": code
        }

        data_json = json.dumps(data)
        base_url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token="
        Token = getToken()

        url = base_url + Token
        res = requests.post(url, data=data_json)

        if res.json().get("errcode") == 0:
            phoneNumber = res.json()["phone_info"]["phoneNumber"]
            phoneNumber = safesql(phoneNumber)
            return jsonify({"phoneNumber": phoneNumber})
        else:
            return jsonify({"error": res.json().get("errmsg")}), 400
    except Exception as e:
        # 调试日志：打印异常信息
        print("发生异常:", str(e))
        return jsonify({"error": str(e)}), 500


@app.route('/api/submitRegisterForm', methods=['POST'])
@check_signature
def submit_register_form():
    try:
        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({'code': 400, 'message': '缺少请求数据'}), 400

        # 验证必填字段
        required_fields = ['parentName', 'contactNumber', 'openid']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'code': 400, 'message': f'缺少必填字段: {field}'}), 400

        # # 验证孩子信息
        # for child in data['children']:
        #     if not all(child.get(field) for field in ['name', 'gender', 'school', 'grade']):
        #         return jsonify({'code': 400, 'message': '孩子信息不完整'}), 400

        print(data)
        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 插入家长信息
        cursor.execute("""
            INSERT INTO Parents (parentName, contactNumber, emergencyContact, openid)
            VALUES (?, ?, ?, ?)
        """, (safesql(data['parentName']), safesql(data['contactNumber']), safesql(data['emergencyContact']),
              safesql(data['openid'])))

        # # 获取刚插入的家长ID
        # cursor.execute("SELECT @@IDENTITY")
        # parent_id = cursor.fetchone()[0]
        # if not parent_id:
        #     raise Exception("无法获取刚插入的家长ID")
        #
        # # 插入孩子信息
        # for child in data['children']:
        #     cursor.execute("""
        #         INSERT INTO Children (parentId, name, gender, school, grade)
        #         VALUES (?, ?, ?, ?, ?)
        #     """, (parent_id, safesql(child['name']), safesql(child['gender']), safesql(child['school']),
        #           safesql(child['grade'])))

        # 提交事务
        conn.commit()

        return jsonify({'code': 200, 'message': '注册成功'})

    except Exception as e:
        # 回滚事务
        if conn:
            conn.rollback()
        print(f"注册失败: {str(e)}")
        return jsonify({'code': 500, 'message': '注册失败'}), 500

    finally:
        # 关闭数据库连接
        if conn:
            conn.close()


@app.route('/api/updateUserInfo', methods=['POST'])
@check_signature
def update_user_info():
    conn = None
    try:
        data = request.json
        if not data:
            return jsonify({'code': 400, 'message': '缺少请求数据'}), 400

        required_fields = ['openid', 'emergencyContact']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'code': 400, 'message': f'缺少必填字段: {field}'}), 400

        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 更新家长紧急联系电话
        cursor.execute("""
            UPDATE Parents 
            SET emergencyContact = ?
            WHERE openid = ?
        """, (safesql(data['emergencyContact']), safesql(data['openid'])))

        # 获取家长ID
        cursor.execute("SELECT id FROM Parents WHERE openid = ?", (safesql(data['openid']),))
        parent = cursor.fetchone()
        if not parent:
            return jsonify({'code': 404, 'message': '未找到家长信息'}), 404
        parent_id = parent[0]

        # 处理每个孩子信息
        for child in data['children']:
            child['gender'] = child.get('gender', '').strip()
            # 检查孩子是否已存在
            cursor.execute("""
                SELECT childId FROM Children 
                WHERE parentId = ? AND name = ?
            """, (parent_id, safesql(child['name'])))
            existing_child = cursor.fetchone()

            if existing_child:
                # 更新现有孩子信息
                cursor.execute("""
                    UPDATE Children 
                    SET school = ?, grade = ?, gender = ?
                    WHERE childId = ?
                """, (safesql(child['school']), safesql(child['grade']),
                      safesql(child['gender']), existing_child[0]))
            else:
                # 插入新孩子信息
                cursor.execute("""
                    INSERT INTO Children 
                    (parentId, name, school, grade, gender)
                    VALUES (?, ?, ?, ?, ?)
                """, (parent_id, safesql(child['name']),
                     safesql(child['school']), safesql(child['grade']),
                     safesql(child['gender'])))

        conn.commit()
        return jsonify({'code': 200, 'message': '更新成功'})

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"更新失败: {str(e)}")
        return jsonify({'code': 500, 'message': '更新失败'}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/checkloginstatus', methods=['POST'])
@check_signature
def checkloginstatus():
    conn = None
    try:
        data = request.json
        if not data or 'openid' not in data:
            return jsonify({'code': 400, 'message': '缺少openid参数'}), 400

        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 查询用户信息和关联的门店信息
        cursor.execute("""
                    SELECT p.parentName, p.contactNumber, s.StoreName, s.Address, s.ID as StoreID, p.emergencyContact
                    FROM Parents p
                    LEFT JOIN Stores s ON p.storeId = s.ID
                    WHERE p.openid = ?
                """, (safesql(data['openid']),))

        result = cursor.fetchone()
        if result:
            return jsonify({
                'code': 200,
                'message': '用户已存在',
                'parentName': result[0],
                'contactNumber': result[1],
                'emergencyContact': result[5],
                'storeInfo': {
                    'storeId': result[4] if result[4] else None,
                    'storeName': result[2] if result[2] else '',
                    'address': result[3] if result[3] else ''
                }
            })
        else:
            return jsonify({'code': 404, 'message': '用户不存在'})

    except Exception as e:
        print(f"检查登录状态失败: {str(e)}")
        return jsonify({'code': 500, 'message': '检查登录状态失败'}), 500

    finally:
        # 关闭数据库连接
        if conn:
            conn.close()


@app.route('/api/getChildrenInfo', methods=['POST'])
@check_signature
def get_children_info():
    conn = None
    try:
        # 获取请求数据
        data = request.json
        if not data or 'openid' not in data:
            return jsonify({'code': 400, 'message': '缺少openid参数'}), 400

        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 查询家长ID
        cursor.execute("SELECT id FROM Parents WHERE openid = ?", (safesql(data['openid']),))
        parent = cursor.fetchone()
        if not parent:
            return jsonify({'code': 404, 'message': '未找到家长信息'}), 404

        parent_id = parent[0]

        # 查询孩子信息
        # 修改查询语句，添加需要的字段
        cursor.execute("""
            SELECT c.name, c.cardNumber, c.cardType, c.school, c.grade, c.gender 
            FROM Children c 
            WHERE c.parentId = ?
        """, (parent_id,))

        children = []
        for row in cursor.fetchall():
            children.append({
                'name': row[0],
                'cardNumber': row[1] if row[1] else '',
                'cardType': row[2] if row[2] else '普通卡',
                'gender': row[5] if row[5] else '',
                'school': row[3] if row[3] else '',
                'grade': row[4] if row[4] else ''
            })

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'children': children
        })

    except Exception as e:
        print(f"获取孩子信息失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取孩子信息失败'}), 500

    finally:
        if conn:
            conn.close()


@app.route('/api/getAdvertisement', methods=['POST'])
@check_signature
def get_advertisement():
    conn = None
    try:
        data = request.json
        if not data or 'adId' not in data:
            return jsonify({'code': 400, 'message': '缺少广告ID参数'}), 400

        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 修改为查询SwiperAds表
        cursor.execute("""
            SELECT AdID, ImageUrl, Title, CreatedAt, description 
            FROM SwiperAds 
            WHERE AdID = ? AND is_active = 1
        """, (data['adId'],))
        ad_info = cursor.fetchone()

        if not ad_info:
            return jsonify({'code': 404, 'message': '广告不存在或已下架'}), 404

        return jsonify({
            'code': 200,
            'data': {
                'adId': ad_info[0],
                'image': ad_info[1],
                'title': ad_info[2],
                'createdAt': ad_info[3].strftime('%Y-%m-%d %H:%M:%S') if ad_info[3] else '',
                'description': ad_info[4]
            }
        })

    except Exception as e:
        print(f"获取广告信息失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取广告信息失败'}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/getMemberCardInfo', methods=['POST'])
@check_signature
def get_member_card_info():
    conn = None
    try:
        data = request.json
        if not data or 'openid' not in data:
            return jsonify({'code': 400, 'message': '缺少openid参数'}), 400

        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 先更新过期会员卡状态
        cursor.execute("""
            UPDATE MemberCards 
            SET Status = 0,
                UpdateTime = GETDATE()
            WHERE EndDate < CAST(GETDATE() AS DATE)
            AND Status = 1
        """)
        conn.commit()

        # 查询家长ID
        cursor.execute("SELECT id FROM Parents WHERE openid = ?", (safesql(data['openid']),))
        parent = cursor.fetchone()
        if not parent:
            return jsonify({'code': 404, 'message': '未找到家长信息'}), 404

        parent_id = parent[0]

        # 查询会员卡信息及关联的孩子信息
        cursor.execute("""
            SELECT 
                mc.CardNumber, 
                mc.CardType, 
                mc.StartDate, 
                mc.EndDate, 
                mc.Status,
                c.name AS childName,
                c.school,
                c.grade,
                c.birthDate
            FROM MemberCards mc
            LEFT JOIN Children c ON mc.CardNumber = c.cardNumber
            WHERE c.parentId = ?
            ORDER BY mc.StartDate DESC
        """, (parent_id,))

        cards = []
        for row in cursor.fetchall():
            cards.append({
                'cardNumber': row[0] or '',
                'cardType': row[1] or '',
                'startDate': format_date(row[2]),
                'endDate': format_date(row[3]),
                'status': '有效' if row[4] == 1 else '无效',
                'childInfo': {
                    'name': row[5] or '',
                    'school': row[6] or '',
                    'grade': row[7] or '',
                    'birthDate': format_date(row[8])
                } if row[5] else None
            })

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'cards': cards
        })

    except Exception as e:
        print(f"获取会员卡信息失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取会员卡信息失败'}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/getStoreAssistants', methods=['POST'])
@check_signature
def get_store_assistants():
    conn = None
    try:
        data = request.json
        if not data or 'storeId' not in data:
            return jsonify({'code': 400, 'message': '缺少门店ID参数'}), 400

        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 查询门店助教信息
        cursor.execute("""
            SELECT 
                sta.AssistantName,
                sta.ContactNumber,
                sta.Email,
                sta.WorkingDays,
                sta.WorkingHours,
                s.StoreName,
                s.Address,
                sta.AvatarURL,
                sta.WechatQRCodeURL
            FROM StoreTeachingAssistants sta
            INNER JOIN Stores s ON sta.StoreID = s.ID
            WHERE sta.StoreID = ? AND sta.Status = 1
            ORDER BY sta.CreateTime DESC
        """, (safesql(data['storeId']),))

        assistants = []
        for row in cursor.fetchall():
            assistants.append({
                'name': row[0] or '',
                'contactNumber': row[1] or '',
                'email': row[2] or '',
                'workingDays': row[3] or '',
                'workingHours': row[4] or '',
                'storeName': row[5] or '',
                'storeAddress': row[6] or '',
                'avatar': row[7] or '',
                'wechatQrcode': row[8] or ''
            })

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'assistants': assistants
        })

    except Exception as e:
        print(f"获取门店助教信息失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取门店助教信息失败'}), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/getHeadquarterContacts', methods=['POST'])
@check_signature
def get_headquarter_contacts():
    conn = None
    try:
        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 查询大区联系人信息
        cursor.execute("""
            SELECT 
              Department,
              ContactName,
              Position,
              ContactNumber,
              PhoneExtension,
              Email,
              AvatarURL,
              WechatQRCodeURL
          FROM HeadquarterContacts where Status=1
        """)

        contacts = []
        for row in cursor.fetchall():
            contacts.append({
                'department': row[0],
                'contactName': row[1],
                'position': row[2],
                'contactNumber': row[3],
                'phoneExtension': row[4],
                'email': row[5],
                'avatarURL': row[6],
                'wechatQRCodeURL': row[7]
            })

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': contacts
        })

    except Exception as e:
        print(f"获取大区联系人失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取联系人信息失败'}), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/video-comments/<video_id>', methods=['GET'])
@check_signature
def get_video_comments(video_id):
    conn = None
    try:
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({"code": 500, "message": "数据库连接失败"}), 500

        cursor = conn.cursor()
        query = """
            SELECT 
                c.id,
                p.parentName AS user_name,
                c.content,
                c.like_count,
                c.created_at,
                c.status,
                c.parent_comment_id,
                c.level,
                c.path
            FROM VideoComments c
            JOIN Parents p ON c.parent_id = p.id
            WHERE c.video_id = ? AND c.status = 1
            ORDER BY c.path, c.created_at
        """

        cursor.execute(query, (safesql(video_id),))
        rows = cursor.fetchall()

        # 构建评论树
        comments = {}
        root_comments = []

        for row in rows:
            comment = {
                "id": row[0],
                "user_name": row[1],
                "content": row[2],
                "like_count": row[3],
                "created_at": format_date(row[4]),
                "status": row[5],
                "replies": []
            }
            comments[row[0]] = comment

            if row[6] is None:  # 根评论
                root_comments.append(comment)
            else:  # 子评论
                parent = comments.get(row[6])
                if parent:
                    parent["replies"].append(comment)

        return jsonify({
            "code": 200,
            "data": root_comments,
            "message": "获取成功"
        })
    except Exception as e:
        return jsonify({"code": 500, "message": str(e)}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/submit-comment', methods=['POST'])
@check_signature
def submit_comment():
    conn = None
    try:
        data = request.json
        required_fields = ['vid', 'content', 'openid']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'code': 400, 'message': f'缺少必填字段: {field}'}), 400


        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500
        cursor = conn.cursor()

        # 获取parent_id
        cursor.execute("SELECT id FROM Parents WHERE openid = ?", (safesql(data['openid']),))

        parent = cursor.fetchone()
        if not parent:
            return jsonify({'code': 404, 'message': '用户不存在'}), 404
        parent_id = parent[0]


        # 处理父评论ID
        parent_comment_id = data.get('parent_comment_id')
        if not parent_comment_id:
            parent_comment_id = None

        level = 1
        path = ''

        if parent_comment_id:
            # 获取父评论的level和path
            cursor.execute("SELECT level, path FROM VideoComments WHERE id = ?", (parent_comment_id,))
            parent_comment = cursor.fetchone()
            if parent_comment:
                level = parent_comment[0] + 1
                path = f"{parent_comment[1]}{parent_comment_id}/"

        # 插入评论
        cursor.execute("""
            INSERT INTO VideoComments (
                video_id, 
                user_id, 
                parent_id, 
                content,
                parent_comment_id,
                level,
                path
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            safesql(data['vid']),
            safesql(data['openid']),
            parent_id,
            safesql(data['content']),
            parent_comment_id,
            level,
            path
        ))

        conn.commit()
        return jsonify({
            'code': 200,
            'message': '评论提交成功，等待审核'
        })

    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({'code': 500, 'message': str(e)}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/like-comment', methods=['POST'])
@check_signature
def like_comment():
    conn = None
    try:
        data = request.json
        if 'comment_id' not in data:
            return jsonify({'code': 400, 'message': '缺少评论ID参数'}), 400

        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()
        cursor.execute("""
            UPDATE VideoComments 
            SET like_count = like_count + 1 
            WHERE id = ?
        """, (data['comment_id'],))

        conn.commit()
        return jsonify({'code': 200, 'message': '点赞成功'})
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({'code': 500, 'message': str(e)}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/getLatestFullScreenAd', methods=['POST'])
@check_signature
def get_latest_full_screen_ad():
    conn = None
    try:
        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 查询最新发布的一个有效全屏广告
        cursor.execute("""
            SELECT TOP 1 
                AdId, AdTitle, AdImageUrl, AdLink, DisplayDuration 
            FROM FullScreenAds 
            WHERE IsActive = 1 
            AND GETDATE() BETWEEN StartTime AND EndTime
            AND (MaxDisplays IS NULL OR CurrentDisplays < MaxDisplays)
            ORDER BY CreatedAt DESC
        """)

        ad = cursor.fetchone()
        if not ad:
            return jsonify({'code': 404, 'message': '暂无有效广告'}), 404

        return jsonify({
            'code': 200,
            'data': {
                'adId': ad[0],
                'title': ad[1],
                'imageUrl': ad[2],
                'link': ad[3],
                'duration': ad[4]
            }
        })

    except Exception as e:
        print(f"获取全屏广告失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取广告失败'}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/getScience', methods=['POST'])
@check_signature
def get_science():
    conn = None
    try:
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()
        cursor.execute("""
            SELECT ID, SortNumber, Title
            FROM science 
            ORDER BY SortNumber ASC
        """)

        result = []
        for row in cursor.fetchall():
            result.append({
                'id': row[0],
                'sortNumber': row[1],
                'title': row[2],
            })

        return jsonify({
            'code': 200,
            'data': result
        })

    except Exception as e:
        return jsonify({'code': 500, 'message': str(e)}), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/ScienceDetails', methods=['POST'])
@check_signature
def ScienceDetails():
    conn = None
    try:
        data = request.json
        if not data or 'scienceId' not in data:
            return jsonify({'code': 400, 'message': '缺少科学内容ID参数'}), 400

        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()
        cursor.execute("""
            SELECT ID, Title, Content, ImageUrl, AudioUrl, CreatedAt 
            FROM Science 
            WHERE ID = ?
        """, (data['scienceId'],))
        science = cursor.fetchone()

        if not science:
            return jsonify({'code': 404, 'message': '科学内容不存在或已下架'}), 404

        return jsonify({
            'code': 200,
            'data': {
                'id': science[0],
                'title': science[1],
                'content': science[2],
                'image': science[3],
                'audio': science[4],
                'createdAt': format_date(science[5])
            }
        })

    except Exception as e:
        print(f"获取科学内容失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取科学内容失败'}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/checkMemberCardByPhone', methods=['POST'])
@check_signature
def check_member_card_by_phone():
    conn = None
    try:
        data = request.json
        if not data or 'phoneNumber' not in data:
            return jsonify({'code': 400, 'message': '缺少手机号参数'}), 400

        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 先更新过期会员卡状态
        cursor.execute("""
            UPDATE MemberCards 
            SET Status = 0,
                UpdateTime = GETDATE()
            WHERE EndDate < CAST(GETDATE() AS DATE)
            AND Status = 1
        """)
        conn.commit()

        # 查询有效会员卡
        cursor.execute("""
            SELECT COUNT(*) 
            FROM Parents p
            JOIN Children c ON p.id = c.parentId
            JOIN MemberCards mc ON c.cardNumber = mc.CardNumber
            WHERE p.contactNumber = ? 
            AND mc.Status = 1
            AND mc.EndDate >= CAST(GETDATE() AS DATE)
        """, (safesql(data['phoneNumber']),))

        count = cursor.fetchone()[0]

        return jsonify({
            'code': 200,
            'hasValidCard': count > 0,
            'message': '查询成功'
        })

    except Exception as e:
        print(f"查询会员卡状态失败: {str(e)}")
        return jsonify({'code': 500, 'message': '查询会员卡状态失败'}), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/getStores', methods=['POST'])
@check_signature
def get_stores():
    conn = None
    try:
        # 连接数据库
        conn = connect_to_sqlserver()
        if not conn:
            return jsonify({'code': 500, 'message': '数据库连接失败'}), 500

        cursor = conn.cursor()

        # 查询门店信息
        cursor.execute("""
            SELECT ID, StoreName, StoreCode 
            FROM Stores 
            WHERE OpeningStatus = 1
            ORDER BY StoreName
        """)

        stores = []
        for row in cursor.fetchall():
            stores.append({
                'id': row[0],
                'storeName': row[1],
                'storeCode': row[2] if row[2] else ''
            })

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'stores': stores
        })

    except Exception as e:
        print(f"获取门店信息失败: {str(e)}")
        return jsonify({'code': 500, 'message': '获取门店信息失败'}), 500
    finally:
        if conn:
            conn.close()

################################################## 多线程处理
def server_forever(mulserver):
    mulserver.start_accepting()
    mulserver._stop_event.wait()


def run(MULTI_PROCESS=False):
    try:
        validate_certificates()
        print("证书验证通过，正在启动HTTPS服务器...")
        if MULTI_PROCESS == False:
            server = pywsgi.WSGIServer(
                ('0.0.0.0', 443),
                app,
                keyfile='key/aikexiaozhan.com.key',
                certfile='key/aikexiaozhan.com_public.crt',
                ca_certs='key/aikexiaozhan.com_chain.crt',
                log=None,  # 禁用开发日志
                error_log=None  # 禁用错误日志
            )
            print("MULTI_PROCESS=False (Gevent WSGIServer)")
            server.serve_forever()
        else:
            mulserver = pywsgi.WSGIServer(('0.0.0.0', 443), app,
                                          keyfile='key/aikexiaozhan.com.key',
                                          certfile='key/aikexiaozhan.com_public.crt',
                                          ca_certs='key/aikexiaozhan.com_chain.crt')
            print("MULTI_PROCESS=True (Gevent WSGIServer)")
            mulserver.start()
            server_forever(mulserver)

            for i in range(cpu_count()):
                p = Process(target=server_forever)
                p.start()
    except Exception as e:
        print(f"HTTPS服务器启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    # 单进程 + 协程
    # run(False)
    # 多进程 + 协程
    run(True)

