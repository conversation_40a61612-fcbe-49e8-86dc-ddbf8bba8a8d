<template>
  <view class="container">
    <view class="canvas-container">
      <view 
        v-for="(sys, index) in bubbleSystems" 
        :key="'sys'+index" 
        class="system-container"
        :style="{ 
          width: sys.width + 'px', 
          height: sys.height + 'px'
        }"
      >
        <view
          v-for="(bubble, bIndex) in sys.bubbles"
          :key="'bubble'+bIndex"
          class="bubble"
          :style="getBubbleStyle(bubble)"
          @click="handleBubbleClick(bubble)"
        >
          <view class="bubble-inner">
            <view class="highlight"></view>
            <view 
              v-for="(s, sIndex) in 5" 
              :key="sIndex" 
              class="stripe"
              :style="getStripeStyle(sIndex)"
            ></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 信息弹窗 -->
    <view v-if="selectedBubble" class="popup-mask" @click="closePopup">
      <view class="popup" @click.stop>
        <image :src="selectedBubble.image" mode="aspectFit" class="popup-image"></image>
        <text class="popup-text">{{ selectedBubble.text }}</text>
        <button class="popup-btn" @click="closePopup">关闭</button>
      </view>
    </view>
  </view>
</template>

<script>
const COLOR_MAP = [
  { left: '#0fb4ff', right: '#ff3384', top: '#ffeb3b' },
  { left: '#ff3384', right: '#0fb4ff', top: '#ff3384' },
  { left: '#ffeb3b', right: '#0fb4ff', top: '#ff3384' }
];

export default {
  data() {
    return {
      bubbleSystems: [],
      selectedBubble: null,
      clickLock: false,
      imageList: [
        '/static/images/bubble1.jpg',
        '/static/images/bubble2.jpg',
        '/static/images/bubble3.jpg',
        '/static/images/bubble4.jpg',
        '/static/images/bubble5.jpg'
      ]
    };
  },
  onReady() {
    this.initSystems();
    this.animate();
  },
  methods: {
    initSystems() {
      const systemInfo = uni.getSystemInfoSync();
      const totalWidth = systemInfo.windowWidth;
      const height = systemInfo.windowHeight;
      
      this.bubbleSystems = [
        { width: totalWidth*0.1, height, bubbles: [] },
        { width: totalWidth*0.18, height, bubbles: [] },
        { width: totalWidth*0.24, height, bubbles: [] },
        { width: totalWidth*0.24, height, bubbles: [] },
        { width: totalWidth*0.24, height, bubbles: [] }
      ];

      const counts = [26, 46, 61, 58, 60];
      this.bubbleSystems.forEach((sys, index) => {
        this.createBubbles(sys, counts[index]);
      });
    },

    createBubbles(sys, count) {
      for (let i = 0; i < count; i++) {
        sys.bubbles.push({
          x: Math.random() * sys.width,
          y: Math.random() * sys.height,
          radius: Math.random() * 15 + 5,
          dx: (Math.random() - 0.5) * 2,
          dy: (Math.random() - 0.5) * 2,
          color: this.getBubbleColor(),
          image: this.imageList[i % 5],
          text: `泡泡主题 ${i+1}\n详细信息...`,
          growthRate: Math.random() * 0.2 - 0.1
        });
      }
    },

    getBubbleColor() {
      const hue = Math.floor(Math.random() * 360);
      return {
        base: `hsla(${hue}, 70%, 50%, 0.2)`,
        highlight: `hsla(${hue}, 100%, 80%, 0.3)`
      };
    },

    getBubbleStyle(bubble) {
      return {
        width: `${bubble.radius * 2}px`,
        height: `${bubble.radius * 2}px`,
        transform: `translate(${bubble.x}px, ${bubble.y}px)`,
        background: bubble.color.base,
        animationDelay: `${Math.random() * -8}s`
      };
    },

    getStripeStyle(index) {
      const colors = COLOR_MAP[Math.floor(Math.random() * 3)];
      const styles = [
        { borderLeft: `15px solid ${colors.left}` },
        { borderRight: `15px solid ${colors.right}` },
        { borderTop: `15px solid ${colors.top}` },
        { borderLeft: `15px solid ${colors.right}`, filter: 'blur(12px)' },
        { borderBottom: '10px solid rgba(255,255,255,0.6)', transform: 'rotate(330deg)' }
      ];
      return {
        inset: `${[10, 10, 20, 30, 10][index]}px`,
        ...styles[index]
      };
    },

    handleBubbleClick(bubble) {
      if (this.clickLock) return;
      this.clickLock = true;
      this.selectedBubble = bubble;
      setTimeout(() => this.clickLock = false, 300);
    },

    updateBubbles(sys) {
      sys.bubbles.forEach(bubble => {
        // 物理运动
        bubble.x += bubble.dx;
        bubble.y += bubble.dy;
        
        // 边界反弹
        if (bubble.x < 0 || bubble.x > sys.width) bubble.dx *= -0.95;
        if (bubble.y < 0 || bubble.y > sys.height) bubble.dy *= -0.95;
        
        // 大小变化
        bubble.radius += bubble.growthRate;
        if (bubble.radius > 20 || bubble.radius < 5) bubble.growthRate *= -1;

        // 碰撞检测
        sys.bubbles.forEach(other => {
          if (bubble !== other) {
            const dx = bubble.x - other.x;
            const dy = bubble.y - other.y;
            const distance = Math.sqrt(dx*dx + dy*dy);
            const minDist = bubble.radius + other.radius;
            
            if (distance < minDist) {
              const angle = Math.atan2(dy, dx);
              const force = (minDist - distance) * 0.5;
              
              bubble.x += Math.cos(angle) * force;
              bubble.y += Math.sin(angle) * force;
              other.x -= Math.cos(angle) * force;
              other.y -= Math.sin(angle) * force;
            }
          }
        });
      });
    },

    animate() {
      const animateFrame = () => {
        this.bubbleSystems.forEach(sys => this.updateBubbles(sys));
        requestAnimationFrame(animateFrame);
      };
      animateFrame();
    },

    closePopup() {
      this.selectedBubble = null;
    }
  }
};
</script>

<style>
.container {
  height: 100vh;
  overflow-x: auto;
  background: #000;
}

.canvas-container {
  height: 100%;
  display: flex;
}

.system-container {
  position: relative;
  flex-shrink: 0;
}

.bubble {
  position: absolute;
  border-radius: 50%;
  box-shadow: inset 0 0 25px rgba(255,255,255,0.25);
  animation: float 8s ease-in-out infinite;
  transform: translate(-50%, -50%);
  will-change: transform;
}

.bubble-inner {
  width: 100%;
  height: 100%;
  position: relative;
}

.highlight {
  position: absolute;
  top: 25%;
  left: 22.5%;
  width: 15%;
  height: 15%;
  border-radius: 50%;
  background: linear-gradient(145deg, 
    rgba(255,255,255,0.9) 0%,
    rgba(255,255,255,0.2) 100%);
  filter: blur(2px);
}

.stripe {
  position: absolute;
  border-radius: 50%;
  filter: blur(4px);
}

.stripe:nth-child(1) { border-left: 15px solid; }
.stripe:nth-child(2) { border-right: 15px solid; }
.stripe:nth-child(3) { border-top: 15px solid; }
.stripe:nth-child(4) { border-left: 15px solid; }
.stripe:nth-child(5) { border-bottom: 10px solid; }

@keyframes float {
  0%, 100% { transform: translate(-50%, calc(-50% - 20px)); }
  50% { transform: translate(-50%, calc(-50% + 20px)); }
}

/* 弹窗样式保持原有 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.popup {
  background: rgba(0,0,0,0.9);
  padding: 20px;
  border-radius: 12px;
  border: 2px solid #fff;
  width: 80%;
  max-width: 400px;
}

.popup-image {
  width: 200px;
  height: 200px;
  margin-bottom: 15px;
}

.popup-text {
  color: #fff;
  font-size: 16px;
  text-align: center;
  margin-bottom: 20px;
  white-space: pre-line;
}

.popup-btn {
  background: #007AFF;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
}
</style>