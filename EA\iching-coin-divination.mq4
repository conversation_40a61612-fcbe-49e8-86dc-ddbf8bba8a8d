//+------------------------------------------------------------------+
//|                                                          aaa.mq4 |
//|                                         Copyright 2024, notandor |
//|                                          https://www.notandor.cn |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, notandor"
#property link      "https://www.notandor.cn"
#property version   "1.00"
#property strict

#include <json.mqh> 
#include <hash.mqh> 
//--- 输入参数
//input string   API_Key = "sk-your-key-here";    // DeepSeek API Key
input string   API_Key = "***********************************";    // DeepSeek API Key

input int      MaxRetries = 3;                  // 最大重试次数
input double   RequestInterval = 60.0;           // 请求间隔(秒)


//--- 全局变量
datetime LastRequestTime = 0;
int LastBarTime = 0; 
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- create timer
   EventSetTimer(60);
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
//--- destroy timer
   EventKillTimer();
   
  }
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
//---
//--- 只在新的K线开始时执行
    if(Time[0] == LastBarTime)return;
    LastBarTime = (int)Time[0];
   
    if(TimeCurrent() - LastRequestTime < RequestInterval)
    {
        Print("请求过频繁，需等待 ", RequestInterval, " 秒");
        return;
    }

    string response;
    int status = SendDeepSeekRequest("北京时间", response);

    if(status == 200)
    {
        Print("response: ", response);
        string result = ParseResponseWithJSON(response);
        if(result != "")
            Print("DeepSeek 回复: ", result);
    }
    else
        Print("请求失败，状态码: ", status);

    LastRequestTime = TimeCurrent();   
  }
//+------------------------------------------------------------------+
//| 发送请求                                                         |
//+------------------------------------------------------------------+
int SendDeepSeekRequest(const string user_message, string &out_response)
{
    string headers = "Content-Type: application/json\r\n";
    headers += "Authorization: Bearer " + API_Key + "\r\n";
    
    string post_data = "{" +
        "\"model\": \"deepseek-chat\"," +
        "\"messages\": [" +
            "{\"role\": \"system\", \"content\": \"You are a helpful assistant.\"}," +
            "{\"role\": \"user\", \"content\": \"" + user_message + "\"}" +
        "]," +
        "\"stream\": false" +
    "}";

    char data[], result[];
    string result_headers;
    int timeout = 5000;
    StringToCharArray(post_data, data, 0, WHOLE_ARRAY, CP_UTF8);
    
    int retry_count = 0;
    int http_status = 0;
    
    while(retry_count < MaxRetries)
    {
        int res = WebRequest(
            "POST",
            "https://api.deepseek.com/chat/completions",
            headers,
            timeout,
            data,
            result,
            result_headers
        );
        
        http_status = res;
        
        if(res == 200)
        {
            out_response = CharArrayToString(result, 0, WHOLE_ARRAY, CP_UTF8);
            break;
        }
        
        if(res == -1)
            Print("WebRequest 错误: ", GetErrorDescription(GetLastError()));
        
        retry_count++;
        Sleep(3000);
    }
    
    return http_status;
}
string GetErrorDescription(int error_code)
{
    switch(error_code)
    {
        case 0:     return "No error";
        case 4001:  return "WebRequest failed - invalid URL";
        case 4014:  return "WebRequest failed - timeout";
        case 4016:  return "WebRequest failed - cannot connect";
        // Add more error codes as needed
        default:    return "Unknown error (" + IntegerToString(error_code) + ")";
    }
}
string ParseResponseWithJSON(const string json)
{
    JSONParser *parser = new JSONParser();
    JSONValue *jv = parser.parse(json);
    
    if(jv == NULL) {
        Print("JSON解析错误:", parser.getErrorCode(), parser.getErrorMessage());
        delete parser;
        return "";
    }
    
    if(!jv.isObject()) {
        Print("响应不是有效的JSON对象");
        delete jv;
        delete parser;
        return "";
    }
    
    JSONObject *jo = jv;
    string result = "";
    
    // 1. 获取choices数组
    JSONValue *choicesValue = jo.getValue("choices");
    if(choicesValue == NULL || !choicesValue.isArray()) {
        Print("未找到choices数组");
        delete jv;
        delete parser;
        return "";
    }
    
    JSONArray *choices = choicesValue;
    if(choices.size() == 0) {
        Print("choices数组为空");
        delete jv;
        delete parser;
        return "";
    }
    
    // 2. 获取第一个choice
    JSONValue *firstChoice = choices.getValue(0);
    if(firstChoice == NULL || !firstChoice.isObject()) {
        Print("第一个choice无效");
        delete jv;
        delete parser;
        return "";
    }
    
    JSONObject *choiceObj = firstChoice;
    
    // 3. 获取message对象
    JSONValue *messageValue = choiceObj.getValue("message");
    if(messageValue == NULL || !messageValue.isObject()) {
        Print("未找到message对象");
        delete jv;
        delete parser;
        return "";
    }
    
    JSONObject *message = messageValue;
    
    // 4. 获取content字段
    if(!message.getString("content", result)) {
        Print("未找到content字段");
    }
    
    delete jv;
    delete parser;
    return result;
}
//+------------------------------------------------------------------+  
//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
  {
//---
   
  }
//+------------------------------------------------------------------+
//| Tester function                                                  |
//+------------------------------------------------------------------+
double OnTester()
  {
//---
   double ret=0.0;
//---

//---
   return(ret);
  }
