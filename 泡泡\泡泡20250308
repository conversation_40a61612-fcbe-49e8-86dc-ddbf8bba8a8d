+function() {
    var _VER_ = navigator.userAgent;
    var _IE6_ = /IE 6/.test(_VER_);
    var STD = !!window.addEventListener;
    var de = document.documentElement;

    _IE6_ && document.execCommand("BackgroundImageCache", false, true);

    var K = 0.999; // 速度衰减系数
    var POW_RATE = 0.0001; // 随机速度变化概率
    var POW_RANGE = 0.8; // 随机速度变化范围

    // 随机生成初始速度
    function SPEED_X() { return 8 + RND() * 4; }
    function SPEED_Y() { return 6 + RND() * 2; }

    var arrBubs = []; // 存储所有泡泡实例
    var iBottom; // 窗口底部边界
    var iRight;  // 窗口右侧边界

    // 数学函数简写
    var SQRT = Math.sqrt;
    var ATAN2 = Math.atan2;
    var SIN = Math.sin;
    var COS = Math.cos;
    var ABS = Math.abs;
    var RND = Math.random;
    var ROUND = Math.round;

    // 定时器函数，每隔指定时间调用回调
    function Timer(call, time) {
        var last = +new Date;
        var delay = 0;
        return setInterval(function() {
            var cur = +new Date;
            delay += (cur - last);
            last = cur;
            if (delay >= time) {
                call();
                delay %= time;
            }
        }, 1);
    }

    // 每 17ms 更新一次动画
    Timer(update, 17);

    // 创建新泡泡
    CreateBubble = function() {
        var bub = new Bubble(10); // 初始直径
        bub.setX(0);
        bub.setY(0);
        bub.vx = SPEED_X();
        bub.vy = SPEED_Y();
        arrBubs.push(bub);
    };

    // 更新动画帧
    function update() {
        var n = arrBubs.length;
        var bub, bub2;

        updateWall(); // 更新窗口边界

        for (var i = 0; i < n; i++) {
            bub = arrBubs[i];
            bub.paint(); // 更新泡泡透明度
            bub.vx *= K; // 速度衰减
            bub.vy *= K;

            // 随机触发大小变化（1% 概率）
            if (RND() < 0.01) {
                var newTargetD = 10 + RND() * 80; // 新目标大小
                bub.setTargetSize(newTargetD);
            }

            // 实现大小渐变
            if (bub.D !== bub.targetD) {
                var delta = bub.targetD - bub.D;
                if (Math.abs(delta) < bub.sizeSpeed) {
                    bub.D = bub.targetD;
                } else {
                    bub.D += delta > 0 ? bub.sizeSpeed : -bub.sizeSpeed;
                }
                bub.updateSize(); // 更新显示大小
            }

            // 随机调整速度
            if (RND() < POW_RATE) {
                bub.vx = SPEED_X() * (1 + RND() * POW_RANGE);
                bub.vy = SPEED_Y() * (1 + RND() * POW_RANGE);
            }

            // 更新位置
            bub.setX(bub.x + bub.vx);
            bub.setY(bub.y + bub.vy);
            checkWalls(bub); // 检查墙壁碰撞
        }

        // 检查泡泡间的碰撞
        for (var i = 0; i < n - 1; i++) {
            bub = arrBubs[i];
            for (var j = i + 1; j < n; j++) {
                bub2 = arrBubs[j];
                checkCollision(bub, bub2);
            }
        }
    }

    // 更新窗口边界
    function updateWall() {
        iRight = de.clientWidth;
        iBottom = de.clientHeight;
    }

    // 检查泡泡与墙壁的碰撞
    function checkWalls(bub) {
        if (bub.x < 0) {
            bub.setX(0);
            bub.vx *= -1;
        } else if (bub.x > iRight - bub.D) {
            bub.setX(iRight - bub.D);
            bub.vx *= -1;
        }

        if (bub.y < 0) {
            bub.setY(0);
            bub.vy *= -1;
        } else if (bub.y > iBottom - bub.D) {
            bub.setY(iBottom - bub.D);
            bub.vy *= -1;
        }
    }

    // 向量旋转计算
    function rotate(x, y, sin, cos, reverse) {
        if (reverse)
            return { x: x * cos + y * sin, y: y * cos - x * sin };
        else
            return { x: x * cos - y * sin, y: y * cos + x * sin };
    }

    // 检查并处理泡泡碰撞
    function checkCollision(bub0, bub1) {
        var dx = bub1.x - bub0.x;
        var dy = bub1.y - bub0.y;
        var dist = SQRT(dx * dx + dy * dy);
        var minDist = (bub0.D + bub1.D) / 2; // 使用平均直径判断碰撞

        if (dist < minDist) {
            var angle = ATAN2(dy, dx);
            var sin = SIN(angle);
            var cos = COS(angle);

            var pos0 = { x: 0, y: 0 };
            var pos1 = rotate(dx, dy, sin, cos, true);

            var vel0 = rotate(bub0.vx, bub0.vy, sin, cos, true);
            var vel1 = rotate(bub1.vx, bub1.vy, sin, cos, true);

            var vxTotal = vel0.x - vel1.x;
            vel0.x = vel1.x;
            vel1.x = vxTotal + vel0.x;

            var absV = ABS(vel0.x) + ABS(vel1.x);
            var overlap = minDist - ABS(pos0.x - pos1.x);

            pos0.x += vel0.x / absV * overlap;
            pos1.x += vel1.x / absV * overlap;

            var pos0F = rotate(pos0.x, pos0.y, sin, cos, false);
            var pos1F = rotate(pos1.x, pos1.y, sin, cos, false);

            bub1.setX(bub0.x + pos1F.x);
            bub1.setY(bub0.y + pos1F.y);
            bub0.setX(bub0.x + pos0F.x);
            bub0.setY(bub0.y + pos0F.y);

            var vel0F = rotate(vel0.x, vel0.y, sin, cos, false);
            var vel1F = rotate(vel1.x, vel1.y, sin, cos, false);

            bub0.vx = vel0F.x;
            bub0.vy = vel0F.y;
            bub1.vx = vel1F.x;
            bub1.vy = vel1F.y;
        }
    }

    var APLHA = 0.8; // 透明度衰减系数
    var POW = [1, APLHA, APLHA * APLHA]; // 透明度层级

    // 泡泡类定义
    function Bubble(initialD) {
        this.D = initialD;        // 当前直径
        this.targetD = initialD;  // 目标直径
        this.sizeSpeed = 2;       // 渐变速度（每帧变化像素数）
        var kOpa = [], kStp = [];
        var arrFlt = [];
        var oBox = document.body.appendChild(document.createElement("div"));

        styBox = oBox.style;
        styBox.position = "absolute";
        styBox.width = this.D + "px";
        styBox.height = this.D + "px";

        // 创建泡泡的子元素
        for (var i = 0; i < 4; i++) {
            var div = document.createElement("div");
            var sty = div.style;

            sty.position = "absolute";
            sty.width = this.D + "px";
            sty.height = this.D + "px";

            oBox.appendChild(div);

            if (i == 3) { // 最后一层为心形图片
                if (_IE6_)
                    sty.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(src=heart.png)";
                else
                    sty.backgroundImage = "url(heart.png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                break;
            }

            kOpa[i] = 3 * RND(); // 随机透明度初始值
            kStp[i] = 0.02 * RND(); // 随机透明度变化步长

            if (STD) {
                sty.backgroundImage = "url(ch" + i + ".png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                arrFlt[i] = sty;
            } else {
                sty.filter = "alpha progid:DXImageTransform.Microsoft.AlphaImageLoader(src=ch" + i + ".png)";
                arrFlt[i] = div.filters.alpha;
            }
        }

        this.styBox = styBox;
        this.oBox = oBox; // 保存容器以便更新子元素
        this.kOpa = kOpa;
        this.kStp = kStp;
        this.arrFlt = arrFlt;
    }

    // 设置泡泡 X 坐标
    Bubble.prototype.setX = function(x) {
        this.x = x;
        this.styBox.left = ROUND(x) + "px";
    };

    // 设置泡泡 Y 坐标
    Bubble.prototype.setY = function(y) {
        this.y = y;
        this.styBox.top = ROUND(y) + "px";
    };

    // 设置目标大小
    Bubble.prototype.setTargetSize = function(newTargetD) {
        this.targetD = newTargetD;
    };

    // 更新泡泡显示大小
    Bubble.prototype.updateSize = function() {
        this.styBox.width = this.D + "px";
        this.styBox.height = this.D + "px";
        for (var i = 0; i < this.oBox.children.length; i++) {
            var div = this.oBox.children[i];
            div.style.width = this.D + "px";
            div.style.height = this.D + "px";
            div.style.backgroundSize = this.D + "px " + this.D + "px";
        }
    };

    // 更新泡泡透明度动画
    Bubble.prototype.paint = function() {
        var i, v;
        for (i = 0; i < 3; i++) {
            v = ABS(SIN(this.kOpa[i] += this.kStp[i] * RND()));
            v *= POW[i];
            v = ((v * 1e4) >> 0) / 1e4;
            this.arrFlt[i].opacity = STD ? v : v * 100;
        }
    };
}();