//+------------------------------------------------------------------+
//|                                                TaijiWuwei_EA.mq4 |
//|                                    太极无为交易法 - 严谨实盘版本 |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "TaijiWuwei Trading System"
#property version   "1.00"
#property strict

//--- 输入参数
input group "=== 信号参数 ==="
input int    FastEMA_Period = 12;        // 快速EMA周期
input int    SlowEMA_Period = 26;        // 慢速EMA周期
input int    RSI_Period = 14;            // RSI周期
input double RSI_UpperLevel = 70.0;      // RSI超买线
input double RSI_LowerLevel = 30.0;      // RSI超卖线
input int    Fractal_Bars = 5;           // 分形确认柱数

input group "=== 风险管理 ==="
input double Risk_Percent = 2.0;         // 每笔交易风险百分比
input int    ATR_Period = 14;            // ATR周期
input double ATR_Multiplier = 2.0;       // ATR止损倍数
input double Max_Daily_Loss = 5.0;       // 最大日亏损百分比
input double Max_Spread = 3.0;           // 最大允许点差(点)

input group "=== 交易控制 ==="
input int    Magic_Number = 20241201;    // EA魔术数字
input bool   Trade_Long = true;          // 允许做多
input bool   Trade_Short = true;         // 允许做空
input int    Max_Orders = 1;             // 最大同时订单数
input bool   Use_TrailingStop = true;    // 使用移动止损

input group "=== 时间过滤 ==="
input bool   Use_TimeFilter = true;      // 使用时间过滤
input int    Start_Hour = 8;             // 开始交易时间(小时)
input int    End_Hour = 22;              // 结束交易时间(小时)
input bool   Avoid_Friday = true;        // 避免周五交易

//--- 全局变量
double g_dailyStartBalance = 0;          // 当日起始余额
datetime g_lastBarTime = 0;              // 上一根K线时间
int g_totalOrders = 0;                   // 当前订单总数

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化日起始余额
    g_dailyStartBalance = AccountBalance();
    
    // 验证输入参数
    if(!ValidateInputs())
    {
        Print("输入参数验证失败，EA停止运行");
        return INIT_PARAMETERS_INCORRECT;
    }
    
    Print("太极无为交易法EA初始化成功");
    Print("账户余额: ", AccountBalance());
    Print("风险设置: ", Risk_Percent, "%");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("太极无为交易法EA停止运行，原因代码: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查新K线
    if(!IsNewBar()) return;
    
    // 更新订单计数
    UpdateOrderCount();
    
    // 检查日亏损熔断
    if(CheckDailyLossLimit())
    {
        Print("触发日亏损熔断，停止交易");
        return;
    }
    
    // 检查点差过滤
    if(!CheckSpreadFilter())
    {
        return;
    }
    
    // 检查时间过滤
    if(!CheckTimeFilter())
    {
        return;
    }
    
    // 更新移动止损
    if(Use_TrailingStop)
    {
        UpdateTrailingStops();
    }
    
    // 检查交易信号
    int signal = GetTradeSignal();
    
    if(signal == 1 && Trade_Long && g_totalOrders < Max_Orders)
    {
        OpenTrade(OP_BUY);
    }
    else if(signal == -1 && Trade_Short && g_totalOrders < Max_Orders)
    {
        OpenTrade(OP_SELL);
    }
}

//+------------------------------------------------------------------+
//| 验证输入参数                                                     |
//+------------------------------------------------------------------+
bool ValidateInputs()
{
    if(FastEMA_Period >= SlowEMA_Period)
    {
        Print("错误: 快速EMA周期必须小于慢速EMA周期");
        return false;
    }
    
    if(Risk_Percent <= 0 || Risk_Percent > 10)
    {
        Print("错误: 风险百分比必须在0-10%之间");
        return false;
    }
    
    if(Max_Daily_Loss <= 0 || Max_Daily_Loss > 20)
    {
        Print("错误: 最大日亏损必须在0-20%之间");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查是否为新K线                                                 |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = Time[0];
    if(currentBarTime != g_lastBarTime)
    {
        g_lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 更新订单计数                                                     |
//+------------------------------------------------------------------+
void UpdateOrderCount()
{
    g_totalOrders = 0;
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == Magic_Number)
            {
                g_totalOrders++;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查日亏损限制                                                   |
//+------------------------------------------------------------------+
bool CheckDailyLossLimit()
{
    // 检查是否为新的一天
    static int lastDay = -1;
    int currentDay = TimeDay(TimeCurrent());
    
    if(currentDay != lastDay)
    {
        g_dailyStartBalance = AccountBalance();
        lastDay = currentDay;
        return false;
    }
    
    // 计算当日亏损百分比
    double currentBalance = AccountBalance();
    double dailyLoss = (g_dailyStartBalance - currentBalance) / g_dailyStartBalance * 100;
    
    return (dailyLoss >= Max_Daily_Loss);
}

//+------------------------------------------------------------------+
//| 检查点差过滤                                                     |
//+------------------------------------------------------------------+
bool CheckSpreadFilter()
{
    double currentSpread = (Ask - Bid) / Point;
    return (currentSpread <= Max_Spread);
}

//+------------------------------------------------------------------+
//| 检查时间过滤                                                     |
//+------------------------------------------------------------------+
bool CheckTimeFilter()
{
    if(!Use_TimeFilter) return true;
    
    int currentHour = TimeHour(TimeCurrent());
    int currentDayOfWeek = TimeDayOfWeek(TimeCurrent());
    
    // 检查周五过滤
    if(Avoid_Friday && currentDayOfWeek == 5) return false;
    
    // 检查交易时间
    return (currentHour >= Start_Hour && currentHour <= End_Hour);
}

//+------------------------------------------------------------------+
//| 获取交易信号                                                     |
//+------------------------------------------------------------------+
int GetTradeSignal()
{
    // 获取EMA值
    double fastEMA_0 = iMA(Symbol(), 0, FastEMA_Period, 0, MODE_EMA, PRICE_CLOSE, 0);
    double fastEMA_1 = iMA(Symbol(), 0, FastEMA_Period, 0, MODE_EMA, PRICE_CLOSE, 1);
    double slowEMA_0 = iMA(Symbol(), 0, SlowEMA_Period, 0, MODE_EMA, PRICE_CLOSE, 0);
    double slowEMA_1 = iMA(Symbol(), 0, SlowEMA_Period, 0, MODE_EMA, PRICE_CLOSE, 1);

    // 获取RSI值
    double rsi_0 = iRSI(Symbol(), 0, RSI_Period, PRICE_CLOSE, 0);
    double rsi_1 = iRSI(Symbol(), 0, RSI_Period, PRICE_CLOSE, 1);

    // 获取ATR值用于波动性过滤
    double atr = iATR(Symbol(), 0, ATR_Period, 1);
    double avgATR = 0;
    for(int i = 1; i <= 10; i++)
    {
        avgATR += iATR(Symbol(), 0, ATR_Period, i);
    }
    avgATR = avgATR / 10;

    // 波动性过滤：当前ATR必须大于平均ATR的80%
    if(atr < avgATR * 0.8) return 0;

    // 检查EMA交叉
    bool bullishCross = (fastEMA_0 > slowEMA_0 && fastEMA_1 <= slowEMA_1);
    bool bearishCross = (fastEMA_0 < slowEMA_0 && fastEMA_1 >= slowEMA_1);

    // 多头信号确认
    if(bullishCross)
    {
        // RSI过滤：避免超买区域
        if(rsi_0 > RSI_UpperLevel) return 0;

        // 分形支撑确认
        if(!CheckFractalSupport()) return 0;

        // 价格行为确认：收盘价必须高于EMA
        if(Close[0] <= fastEMA_0) return 0;

        // 趋势强度确认
        if(!CheckTrendStrength(1)) return 0;

        return 1; // 买入信号
    }

    // 空头信号确认
    if(bearishCross)
    {
        // RSI过滤：避免超卖区域
        if(rsi_0 < RSI_LowerLevel) return 0;

        // 分形阻力确认
        if(!CheckFractalResistance()) return 0;

        // 价格行为确认：收盘价必须低于EMA
        if(Close[0] >= fastEMA_0) return 0;

        // 趋势强度确认
        if(!CheckTrendStrength(-1)) return 0;

        return -1; // 卖出信号
    }

    return 0; // 无信号
}

//+------------------------------------------------------------------+
//| 检查分形支撑                                                     |
//+------------------------------------------------------------------+
bool CheckFractalSupport()
{
    for(int i = 2; i <= 10; i++) // 检查最近10根K线的分形
    {
        bool isFractalLow = true;

        // 检查分形低点
        for(int j = 1; j <= Fractal_Bars/2; j++)
        {
            if(Low[i] >= Low[i-j] || Low[i] >= Low[i+j])
            {
                isFractalLow = false;
                break;
            }
        }

        if(isFractalLow && Low[0] > Low[i])
        {
            return true; // 找到有效分形支撑
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查分形阻力                                                     |
//+------------------------------------------------------------------+
bool CheckFractalResistance()
{
    for(int i = 2; i <= 10; i++) // 检查最近10根K线的分形
    {
        bool isFractalHigh = true;

        // 检查分形高点
        for(int j = 1; j <= Fractal_Bars/2; j++)
        {
            if(High[i] <= High[i-j] || High[i] <= High[i+j])
            {
                isFractalHigh = false;
                break;
            }
        }

        if(isFractalHigh && High[0] < High[i])
        {
            return true; // 找到有效分形阻力
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查趋势强度                                                     |
//+------------------------------------------------------------------+
bool CheckTrendStrength(int direction)
{
    double fastEMA = iMA(Symbol(), 0, FastEMA_Period, 0, MODE_EMA, PRICE_CLOSE, 0);
    double slowEMA = iMA(Symbol(), 0, SlowEMA_Period, 0, MODE_EMA, PRICE_CLOSE, 0);

    // 计算EMA间距
    double emaDistance = MathAbs(fastEMA - slowEMA) / Point;

    // 获取平均EMA间距
    double avgDistance = 0;
    for(int i = 1; i <= 20; i++)
    {
        double fast = iMA(Symbol(), 0, FastEMA_Period, 0, MODE_EMA, PRICE_CLOSE, i);
        double slow = iMA(Symbol(), 0, SlowEMA_Period, 0, MODE_EMA, PRICE_CLOSE, i);
        avgDistance += MathAbs(fast - slow) / Point;
    }
    avgDistance = avgDistance / 20;

    // 趋势强度必须大于平均值的120%
    return (emaDistance > avgDistance * 1.2);
}

//+------------------------------------------------------------------+
//| 开仓交易                                                         |
//+------------------------------------------------------------------+
void OpenTrade(int orderType)
{
    double lotSize = CalculateLotSize();
    if(lotSize <= 0) return;

    double price, sl, tp;
    string comment = "TaijiWuwei_" + TimeToString(TimeCurrent(), TIME_SECONDS);

    if(orderType == OP_BUY)
    {
        price = Ask;
        sl = CalculateStopLoss(OP_BUY, price);
        tp = CalculateTakeProfit(OP_BUY, price, sl);

        int ticket = OrderSend(Symbol(), OP_BUY, lotSize, price, 3, sl, tp, comment, Magic_Number, 0, clrBlue);

        if(ticket > 0)
        {
            Print("买入订单成功: ", ticket, " 手数: ", lotSize, " 价格: ", price, " 止损: ", sl, " 止盈: ", tp);
        }
        else
        {
            Print("买入订单失败，错误代码: ", GetLastError());
        }
    }
    else if(orderType == OP_SELL)
    {
        price = Bid;
        sl = CalculateStopLoss(OP_SELL, price);
        tp = CalculateTakeProfit(OP_SELL, price, sl);

        int ticket = OrderSend(Symbol(), OP_SELL, lotSize, price, 3, sl, tp, comment, Magic_Number, 0, clrRed);

        if(ticket > 0)
        {
            Print("卖出订单成功: ", ticket, " 手数: ", lotSize, " 价格: ", price, " 止损: ", sl, " 止盈: ", tp);
        }
        else
        {
            Print("卖出订单失败，错误代码: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| 计算手数                                                         |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    double balance = AccountBalance();
    double riskAmount = balance * Risk_Percent / 100;

    // 获取ATR计算止损距离
    double atr = iATR(Symbol(), 0, ATR_Period, 1);
    double stopDistance = atr * ATR_Multiplier;

    // 计算每点价值
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    if(tickValue == 0) tickValue = 1;

    // 计算手数
    double lotSize = riskAmount / (stopDistance / Point * tickValue);

    // 标准化手数
    double minLot = MarketInfo(Symbol(), MODE_MINLOT);
    double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
    double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

    lotSize = MathMax(minLot, MathMin(maxLot, NormalizeDouble(lotSize / lotStep, 0) * lotStep));

    return lotSize;
}

//+------------------------------------------------------------------+
//| 计算止损                                                         |
//+------------------------------------------------------------------+
double CalculateStopLoss(int orderType, double entryPrice)
{
    double atr = iATR(Symbol(), 0, ATR_Period, 1);
    double stopDistance = atr * ATR_Multiplier;

    double sl = 0;

    if(orderType == OP_BUY)
    {
        sl = entryPrice - stopDistance;

        // 确保止损不违反最小距离要求
        double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
        if(entryPrice - sl < minDistance)
        {
            sl = entryPrice - minDistance;
        }
    }
    else if(orderType == OP_SELL)
    {
        sl = entryPrice + stopDistance;

        // 确保止损不违反最小距离要求
        double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
        if(sl - entryPrice < minDistance)
        {
            sl = entryPrice + minDistance;
        }
    }

    return NormalizeDouble(sl, Digits);
}

//+------------------------------------------------------------------+
//| 计算止盈                                                         |
//+------------------------------------------------------------------+
double CalculateTakeProfit(int orderType, double entryPrice, double stopLoss)
{
    double stopDistance = MathAbs(entryPrice - stopLoss);
    double profitDistance = stopDistance * 2.0; // 风险回报比 1:2

    double tp = 0;

    if(orderType == OP_BUY)
    {
        tp = entryPrice + profitDistance;
    }
    else if(orderType == OP_SELL)
    {
        tp = entryPrice - profitDistance;
    }

    return NormalizeDouble(tp, Digits);
}

//+------------------------------------------------------------------+
//| 更新移动止损                                                     |
//+------------------------------------------------------------------+
void UpdateTrailingStops()
{
    double atr = iATR(Symbol(), 0, ATR_Period, 1);
    double trailDistance = atr * ATR_Multiplier * 0.5; // 移动止损距离为止损距离的一半

    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(!OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) continue;
        if(OrderSymbol() != Symbol() || OrderMagicNumber() != Magic_Number) continue;

        double newSL = 0;
        bool modifyOrder = false;

        if(OrderType() == OP_BUY)
        {
            // 买单移动止损
            newSL = Bid - trailDistance;

            // 只有当新止损高于当前止损时才修改
            if(newSL > OrderStopLoss() + Point)
            {
                // 确保不违反最小距离
                double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
                if(Bid - newSL >= minDistance)
                {
                    modifyOrder = true;
                }
            }
        }
        else if(OrderType() == OP_SELL)
        {
            // 卖单移动止损
            newSL = Ask + trailDistance;

            // 只有当新止损低于当前止损时才修改
            if(newSL < OrderStopLoss() - Point || OrderStopLoss() == 0)
            {
                // 确保不违反最小距离
                double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
                if(newSL - Ask >= minDistance)
                {
                    modifyOrder = true;
                }
            }
        }

        if(modifyOrder)
        {
            newSL = NormalizeDouble(newSL, Digits);
            bool result = OrderModify(OrderTicket(), OrderOpenPrice(), newSL, OrderTakeProfit(), 0, clrYellow);

            if(result)
            {
                Print("移动止损成功 - 订单: ", OrderTicket(), " 新止损: ", newSL);
            }
            else
            {
                Print("移动止损失败 - 订单: ", OrderTicket(), " 错误: ", GetLastError());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 获取当前持仓盈亏                                                 |
//+------------------------------------------------------------------+
double GetCurrentProfit()
{
    double totalProfit = 0;

    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == Magic_Number)
            {
                totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
            }
        }
    }

    return totalProfit;
}

//+------------------------------------------------------------------+
//| 市场状态分析                                                     |
//+------------------------------------------------------------------+
string GetMarketCondition()
{
    double fastEMA = iMA(Symbol(), 0, FastEMA_Period, 0, MODE_EMA, PRICE_CLOSE, 0);
    double slowEMA = iMA(Symbol(), 0, SlowEMA_Period, 0, MODE_EMA, PRICE_CLOSE, 0);
    double rsi = iRSI(Symbol(), 0, RSI_Period, PRICE_CLOSE, 0);
    double atr = iATR(Symbol(), 0, ATR_Period, 1);

    string condition = "";

    // 趋势判断
    if(fastEMA > slowEMA)
        condition += "上升趋势 ";
    else
        condition += "下降趋势 ";

    // RSI状态
    if(rsi > RSI_UpperLevel)
        condition += "超买 ";
    else if(rsi < RSI_LowerLevel)
        condition += "超卖 ";
    else
        condition += "中性 ";

    // 波动性
    double avgATR = 0;
    for(int i = 1; i <= 10; i++)
    {
        avgATR += iATR(Symbol(), 0, ATR_Period, i);
    }
    avgATR = avgATR / 10;

    if(atr > avgATR * 1.2)
        condition += "高波动";
    else if(atr < avgATR * 0.8)
        condition += "低波动";
    else
        condition += "正常波动";

    return condition;
}
