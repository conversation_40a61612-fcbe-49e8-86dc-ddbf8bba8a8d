//+------------------------------------------------------------------+
//|                                          TestSignal_Indicator.mq4 |
//|                                    测试信号指标 - 配合EA使用     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Test Signal Indicator"
#property version   "1.00"
#property strict
#property indicator_separate_window
#property indicator_buffers 6
#property indicator_plots   3

// 绘图设置
#property indicator_label1  "买入信号"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "卖出信号"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

#property indicator_label3  "平仓信号"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrYellow
#property indicator_style3  STYLE_SOLID
#property indicator_width3  3

//--- 输入参数
input int    MA_Fast_Period = 12;        // 快速移动平均周期
input int    MA_Slow_Period = 26;        // 慢速移动平均周期
input int    RSI_Period = 14;            // RSI周期
input double RSI_Upper = 70.0;           // RSI超买线
input double RSI_Lower = 30.0;           // RSI超卖线
input int    Signal_Bars = 3;            // 信号确认K线数

//--- 指标缓冲区
double BuySignalBuffer[];      // 缓冲区0 - 买入信号
double SellSignalBuffer[];     // 缓冲区1 - 卖出信号
double Buffer2[];              // 缓冲区2 - 未使用
double Buffer3[];              // 缓冲区3 - 未使用
double CloseSellBuffer[];      // 缓冲区4 - 平空信号
double CloseBuyBuffer[];       // 缓冲区5 - 平多信号

//--- 全局变量
int g_lastSignalBar = -1;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // 设置缓冲区
    SetIndexBuffer(0, BuySignalBuffer);
    SetIndexBuffer(1, SellSignalBuffer);
    SetIndexBuffer(2, Buffer2);
    SetIndexBuffer(3, Buffer3);
    SetIndexBuffer(4, CloseSellBuffer);  // 平空信号
    SetIndexBuffer(5, CloseBuyBuffer);   // 平多信号
    
    // 设置箭头代码
    SetIndexArrow(0, 233);  // 向上箭头
    SetIndexArrow(1, 234);  // 向下箭头
    SetIndexArrow(2, 159);  // 平仓信号
    
    // 设置空值
    SetIndexEmptyValue(0, EMPTY_VALUE);
    SetIndexEmptyValue(1, EMPTY_VALUE);
    SetIndexEmptyValue(2, EMPTY_VALUE);
    
    // 设置指标名称
    IndicatorShortName("测试信号指标(" + IntegerToString(MA_Fast_Period) + "," + IntegerToString(MA_Slow_Period) + ")");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    int limit = rates_total - prev_calculated;
    if(prev_calculated > 0) limit++;
    
    // 确保有足够的数据
    if(rates_total < MA_Slow_Period + RSI_Period + Signal_Bars)
        return 0;
    
    // 计算信号
    for(int i = limit - 1; i >= 0; i--)
    {
        // 初始化缓冲区
        BuySignalBuffer[i] = EMPTY_VALUE;
        SellSignalBuffer[i] = EMPTY_VALUE;
        CloseSellBuffer[i] = EMPTY_VALUE;  // 平空信号
        CloseBuyBuffer[i] = EMPTY_VALUE;   // 平多信号
        
        // 跳过最新的几根K线，避免信号频繁变化
        if(i < Signal_Bars) continue;
        
        // 获取技术指标值
        double ma_fast_current = iMA(Symbol(), 0, MA_Fast_Period, 0, MODE_SMA, PRICE_CLOSE, i);
        double ma_fast_previous = iMA(Symbol(), 0, MA_Fast_Period, 0, MODE_SMA, PRICE_CLOSE, i + 1);
        double ma_slow_current = iMA(Symbol(), 0, MA_Slow_Period, 0, MODE_SMA, PRICE_CLOSE, i);
        double ma_slow_previous = iMA(Symbol(), 0, MA_Slow_Period, 0, MODE_SMA, PRICE_CLOSE, i + 1);
        
        double rsi_current = iRSI(Symbol(), 0, RSI_Period, PRICE_CLOSE, i);
        
        // 检查移动平均交叉
        bool bullish_cross = (ma_fast_current > ma_slow_current && ma_fast_previous <= ma_slow_previous);
        bool bearish_cross = (ma_fast_current < ma_slow_current && ma_fast_previous >= ma_slow_previous);
        
        // 生成买入信号
        if(bullish_cross && rsi_current < RSI_Upper && rsi_current > 40)
        {
            // 确认信号强度
            if(ConfirmBuySignal(i))
            {
                BuySignalBuffer[i] = low[i] - 20 * Point;
            }
        }
        
        // 生成卖出信号
        if(bearish_cross && rsi_current > RSI_Lower && rsi_current < 60)
        {
            // 确认信号强度
            if(ConfirmSellSignal(i))
            {
                SellSignalBuffer[i] = high[i] + 20 * Point;
            }
        }
        
        // 生成平空信号（基于RSI极值或趋势反转）
        if(CheckCloseSellSignal(i))
        {
            CloseSellBuffer[i] = high[i] + 10 * Point;
        }

        // 生成平多信号（基于RSI极值或趋势反转）
        if(CheckCloseBuySignal(i))
        {
            CloseBuyBuffer[i] = low[i] - 10 * Point;
        }
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| 确认买入信号                                                     |
//+------------------------------------------------------------------+
bool ConfirmBuySignal(int shift)
{
    // 检查价格动量
    double price_momentum = (Close[shift] - Close[shift + 3]) / Close[shift + 3] * 100;
    if(price_momentum < 0.1) return false;  // 价格动量不足
    
    // 检查成交量（如果可用）
    if(Volume[shift] < Volume[shift + 1] * 1.2) return false;  // 成交量确认
    
    // 检查K线形态
    double body_size = MathAbs(Close[shift] - Open[shift]);
    double candle_range = High[shift] - Low[shift];
    if(body_size < candle_range * 0.6) return false;  // K线实体不够大
    
    return true;
}

//+------------------------------------------------------------------+
//| 确认卖出信号                                                     |
//+------------------------------------------------------------------+
bool ConfirmSellSignal(int shift)
{
    // 检查价格动量
    double price_momentum = (Close[shift] - Close[shift + 3]) / Close[shift + 3] * 100;
    if(price_momentum > -0.1) return false;  // 价格动量不足
    
    // 检查成交量（如果可用）
    if(Volume[shift] < Volume[shift + 1] * 1.2) return false;  // 成交量确认
    
    // 检查K线形态
    double body_size = MathAbs(Close[shift] - Open[shift]);
    double candle_range = High[shift] - Low[shift];
    if(body_size < candle_range * 0.6) return false;  // K线实体不够大
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查平空信号                                                     |
//+------------------------------------------------------------------+
bool CheckCloseSellSignal(int shift)
{
    double rsi = iRSI(Symbol(), 0, RSI_Period, PRICE_CLOSE, shift);

    // RSI超买时平空
    if(rsi > 75) return true;

    // 趋势转多时平空
    double ma_fast = iMA(Symbol(), 0, MA_Fast_Period, 0, MODE_SMA, PRICE_CLOSE, shift);
    double ma_slow = iMA(Symbol(), 0, MA_Slow_Period, 0, MODE_SMA, PRICE_CLOSE, shift);

    if(ma_fast > ma_slow * 1.015) return true;  // 快线明显高于慢线时平空

    // 随机平空信号
    if(shift % 60 == 0 && MathRand() % 100 < 8) return true;

    return false;
}

//+------------------------------------------------------------------+
//| 检查平多信号                                                     |
//+------------------------------------------------------------------+
bool CheckCloseBuySignal(int shift)
{
    double rsi = iRSI(Symbol(), 0, RSI_Period, PRICE_CLOSE, shift);

    // RSI超卖时平多
    if(rsi < 25) return true;

    // 趋势转空时平多
    double ma_fast = iMA(Symbol(), 0, MA_Fast_Period, 0, MODE_SMA, PRICE_CLOSE, shift);
    double ma_slow = iMA(Symbol(), 0, MA_Slow_Period, 0, MODE_SMA, PRICE_CLOSE, shift);

    if(ma_fast < ma_slow * 0.985) return true;  // 快线明显低于慢线时平多

    // 随机平多信号
    if(shift % 70 == 0 && MathRand() % 100 < 8) return true;

    return false;
}

//+------------------------------------------------------------------+
//| 获取指标值（供EA调用）                                           |
//+------------------------------------------------------------------+
double GetSignalValue(int buffer, int shift)
{
    switch(buffer)
    {
        case 0: return BuySignalBuffer[shift];
        case 1: return SellSignalBuffer[shift];
        case 4: return CloseSellBuffer[shift];  // 平空信号
        case 5: return CloseBuyBuffer[shift];   // 平多信号
        default: return EMPTY_VALUE;
    }
}
