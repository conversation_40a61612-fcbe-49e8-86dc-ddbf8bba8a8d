<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科学探索中心</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            background: radial-gradient(circle at center, #0a0a2a, #000);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            position: relative;
        }
        .container {
            display: flex;
            gap: 5vw; 
            height: 100%;
            align-items: center;
            position: relative;
            z-index: 20;
        }
        .btn {
            width: 45vw;
            height: 30vh;
            min-width: 200px;
            min-height: 250px;
            background: linear-gradient(145deg, #ff416c 0%, #ff4b2b 60%, #ffb347 100%);
            border: none;
            border-radius: 22px;
            color: white;
            font-size: 3vw;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            text-decoration: none;
            transition: all 0.25s cubic-bezier(.25,.8,.25,1);
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 24px 0 rgba(255,75,43,0.25),
                0 2px 4px 0 rgba(0,0,0,0.18),
                0 1.5px 0 #fff3 inset,
                0 1.5px 0 #fff6 inset,
                0 0.5px 0 #fff9 inset;
            border: 1.5px solid rgba(255,255,255,0.18);
        }
        .btn.touch-active {
            transform: perspective(600px) translateY(2px) scale(0.98) rotateX(1deg);
            box-shadow:
                0 2px 6px 0 rgba(255,75,43,0.18),
                0 1px 2px 0 rgba(0,0,0,0.18),
                0 1px 0 #fff3 inset;
            filter: brightness(0.97);
        }
        .btn:hover {
            transform: perspective(600px) translateY(-8px) scale(1.04) rotateX(4deg);
            box-shadow:
                0 16px 32px 0 rgba(255,75,43,0.32),
                0 4px 12px 0 rgba(0,0,0,0.22),
                0 2px 0 #fff6 inset,
                0 2px 0 #fff9 inset;
            filter: brightness(1.08);
        }
        .btn:active {
            transform: perspective(600px) translateY(2px) scale(0.98) rotateX(1deg);
            box-shadow:
                0 2px 6px 0 rgba(255,75,43,0.18),
                0 1px 2px 0 rgba(0,0,0,0.18),
                0 1px 0 #fff3 inset;
            filter: brightness(0.97);
        }
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 22px;
            background: linear-gradient(
                120deg,
                rgba(255,255,255,0.22) 0%,
                rgba(255,255,255,0.10) 30%,
                rgba(255,255,255,0.04) 60%,
                rgba(255,255,255,0.01) 100%
            );
            pointer-events: none;
            z-index: 1;
        }
        .btn::after {
            content: '';
            position: absolute;
            left: 15%;
            top: 10%;
            width: 70%;
            height: 18%;
            border-radius: 50%;
            background: linear-gradient(90deg,rgba(255,255,255,0.38),rgba(255,255,255,0.08));
            filter: blur(1.5px);
            opacity: 0.7;
            pointer-events: none;
            z-index: 2;
        }
        .btn span {
            position: relative;
            z-index: 3;
            letter-spacing: 2px;
            text-shadow: 0 2px 8px rgba(255,75,43,0.15), 0 1px 0 #fff6;
        }
        
        /* 下拉菜单样式 */
        .dropdown-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .dropdown-menu {
            position: absolute;
            top: calc(100% + 15px);
            width: 100%;
            background: rgba(20, 25, 50, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 15px 0;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);
            opacity: 0;
            transform: translateY(-20px);
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 100;
            border: 1px solid rgba(255, 255, 255, 0.15);
            overflow: hidden;
        }
        .dropdown-container.active .dropdown-menu {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }
        .dropdown-menu::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff416c, #ff4b2b, #ffb347);
        }
        .menu-item {
            padding: 20px 30px;
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            font-size: 2.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .menu-item:last-child {
            border-bottom: none;
        }
        .menu-item i {
            margin-right: 15px;
            font-size: 2.9rem;
            color: #ffb347;
        }
        .menu-item:hover {
            background: rgba(255, 255, 255, 0.05);
            color: white;
            padding-left: 30px;
            padding-right: 30px;
        }
        .menu-item:hover i {
            color: #ff416c;
        }
        
        /* 科幻背景元素 */
        .sci-fi-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
            overflow: hidden;
        }
        .particle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
            animation: float 15s infinite linear;
        }
        @keyframes float {
            0% { transform: translateY(0) translateX(0); }
            25% { transform: translateY(-20px) translateX(10px); }
            50% { transform: translateY(0) translateX(20px); }
            75% { transform: translateY(20px) translateX(10px); }
            100% { transform: translateY(0) translateX(0); }
        }
        
        /* 标题样式 */
        .title {
            position: absolute;
            top: 40px;
            left: 0;
            width: 100%;
            text-align: center;
            color: white;
            font-size: 3.5rem;
            text-shadow: 0 0 15px rgba(0, 150, 255, 0.8);
            background: linear-gradient(90deg, #ff8a00, #e52e71, #22c1c3);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            z-index: 10;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                gap: 5vh;
            }
            .btn {
                width: 80vw;
                font-size: 2.2rem;
                height: 25vh;
            }
            .title {
                font-size: 2.5rem;
                top: 20px;
            }
        }
    </style>
</head>
<body oncontextmenu="return false" onselectstart="return false">
    <div class="container">
        <div class="dropdown-container" id="science-history">
            <button class="btn">
                <span><i class="fas fa-book-open"></i> 横向多态系：科普知识</span>
            </button>
            <div class="dropdown-menu">
                <div class="menu-item" onclick="navigateTo('page1.html')">
                    <i class="fas fa-file-alt"></i> 人类文明（图文音频）
                </div>
                <div class="menu-item" onclick="navigateTo('page2.html')">
                    <i class="fas fa-video"></i> 人类文明（视频播放）
                </div>
            </div>
        </div>
        
        <div class="dropdown-container" id="mysteries">
            <button class="btn">
                <span><i class="fas fa-flask"></i> 纵向演化系：科学实验</span>
            </button>
            <div class="dropdown-menu">
                <div class="menu-item" onclick="navigateTo('page3.html')">
                    <i class="fas fa-apple-alt"></i> 小学科技基础
                </div>
                <div class="menu-item" onclick="navigateTo('page4.html')">
                    <i class="fas fa-rocket"></i> 小学进阶科学
                </div>
                <div class="menu-item">
                    <i class="fas fa-atom"></i> 物理分类拓展
                </div>
                <div class="menu-item">
                    <i class="fas fa-microscope"></i> 化学生物研究
                </div>
				<div class="menu-item">
				    <i class="fas fa-brain"></i> 科学高级课题
				</div>
            </div>
        </div>
    </div>
    
    <div class="sci-fi-bg" id="particles"></div>

    <script>
        // 导航函数
        function navigateTo(url) {
            window.location.href = url;
        }
        
        // 切换下拉菜单显示状态
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            const isActive = dropdown.classList.contains('active');
            
            // 关闭所有下拉菜单
            document.querySelectorAll('.dropdown-container').forEach(d => {
                d.classList.remove('active');
            });
            
            // 切换当前下拉菜单
            if (!isActive) {
                dropdown.classList.add('active');
            }
        }
        
        // 添加按钮点击事件
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                const dropdown = this.closest('.dropdown-container');
                toggleDropdown(dropdown.id);
            });
        });
        
        // 点击页面其他地方关闭下拉菜单
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown-container')) {
                document.querySelectorAll('.dropdown-container').forEach(d => {
                    d.classList.remove('active');
                });
            }
        });
        
        // 添加触摸屏支持
        document.querySelectorAll('.btn').forEach(btn => {
            // 触摸开始
            btn.addEventListener('touchstart', function(e) {
                e.preventDefault();
                this.classList.add('touch-active');
            }, {passive: false});
            
            // 触摸结束
            btn.addEventListener('touchend', function(e) {
                e.preventDefault();
                this.classList.remove('touch-active');
                if (!document.querySelector('.touch-active')) {
                    this.click();
                }
            }, {passive: false});
            
            // 触摸取消
            btn.addEventListener('touchcancel', function() {
                this.classList.remove('touch-active');
            });
        });
        
        // 创建科幻粒子背景
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.classList.add('particle');
                
                // 随机大小和位置
                const size = Math.random() * 6 + 2;
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;
                const delay = Math.random() * 15;
                
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;
                particle.style.animationDelay = `${delay}s`;
                particle.style.opacity = Math.random() * 0.5 + 0.3;
                
                particlesContainer.appendChild(particle);
            }
        }
        
        // 初始化页面
        window.addEventListener('load', function() {
            createParticles();
        });
    </script>
</body>
</html>