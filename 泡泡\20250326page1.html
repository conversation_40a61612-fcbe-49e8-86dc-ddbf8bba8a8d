<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
<title>Bubbles</title>
<style>
html, body {
    border: none;
    overflow: hidden;
    height: 100%;
    margin: 0;
    padding: 0;
}

body { 
    background: url(BG.jpg) bottom; 
    position: relative;
    display: flex;
    flex-direction: row; /* 让区域水平排列 */
}

/* 5 个水平排列的区域 */
.region {
    height: 100%; /* 区域占满整个高度 */
    border-left: 2px solid rgba(33, 33, 33, 0.8);
    position: relative;
}
/* 利用伪元素在每个分区底部添加标注，类似CAD测量距离的风格 */
.region::after {
    content: attr(data-label);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.85);
    background: rgba(0, 0, 0, 0.5);
    padding: 4px 0;
    border-top: 1px dashed rgba(255, 255, 255, 0.7);
}

/* 区域背景色均采用半透明黑色 */
.region:nth-child(1),
.region:nth-child(2),
.region:nth-child(3),
.region:nth-child(4),
.region:nth-child(5) {
    background: rgba(0, 0, 0, 0.2);
}
.region:nth-child(1) { width: 10%; }
.region:nth-child(2) { width: 18%; }
.region:nth-child(3) { width: 24%; }
.region:nth-child(4) { width: 24%; }
.region:nth-child(5) { width: 24%; }

/* 美化弹出窗口 */
.popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #ffffff, #f7f7f7);
    border: 2px solid #333;
    border-radius: 8px;
    padding: 30px;
    z-index: 1000;
    width: 80%;
    max-width: 1000px;
    max-height: 80vh;      /* 最大高度80%视口高度 */
    overflow-y: auto;      /* 超出内容出现竖向滚动条 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-align: center;
}

.popup h2 {
    margin-top: 0;
    color: #333;
}

.popup p {
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    text-align: left;
    margin: 15px 0;
}

.popup img {
    max-width: 100%;
    height: auto;
    margin: 20px 0;
}

#closeButton {
    display: inline-block;
    background-color: #333;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 18px;
    margin-top: 20px;
}

#closeButton:hover {
    background-color: #555;
}

/* 返回首页按钮样式，固定在页面右下角 */
.back-btn {
    position: fixed;
    right: 20px;
    bottom: 35px;
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    padding: 10px 20px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    text-decoration: none;
    transition: background 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}
.back-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
}
</style>
</head>

<body onload="Demo()">
    <!-- 5 个水平排列的区域，设置 data-label 属性标注分区名称 -->
    <div class="region" data-label="公元前2.6万年-公元0年"></div>
    <div class="region" data-label="50-1798年"></div>
    <div class="region" data-label="1800-1899年"></div>
    <div class="region" data-label="1900-1949年"></div>
    <div class="region" data-label="1949-2024年"></div>

    <!-- 弹出窗口，内容为图文信息，文字可能较长 -->
    <div class="popup" id="bubblePopup">
        <h2>泡泡信息</h2>
        <p id="bubbleId"></p>
        <!-- 这里可以显示1000字左右的长文本 -->
        <p id="bubbleText">
            这里是详细的泡泡信息……
        </p>
        <img id="bubbleImage" src="" alt="Bubble Image">
        <button id="closeButton">关闭</button>
    </div>
    <!-- 返回首页按钮，固定在右下角 -->
    <a href="index.html" class="back-btn">HOME</a>

<script>
// 定义每个区域目标泡泡数及当前计数
var bubbleTarget = [26, 46, 61, 58, 60]; 
var bubbleCount = [0, 0, 0, 0, 0];
var bubbleIdCounter = 1; // 唯一ID计数器
var currentRegionIndex = 0; // 当前生成泡泡的区域索引

function Demo() {
    if (currentRegionIndex < bubbleTarget.length) {
        if (bubbleCount[currentRegionIndex] < bubbleTarget[currentRegionIndex]) {
            CreateBubble(currentRegionIndex);
            bubbleCount[currentRegionIndex]++;
        }
        if (bubbleCount[currentRegionIndex] < bubbleTarget[currentRegionIndex]) {
            setTimeout(Demo, 0);
        } else {
            currentRegionIndex++;
            setTimeout(Demo, 0);
        }
    }
}

+function() {
    var _VER_ = navigator.userAgent;
    var _IE6_ = /IE 6/.test(_VER_);
    var STD = !!window.addEventListener;
    var de = document.documentElement;

    _IE6_ && document.execCommand("BackgroundImageCache", false, true);

    var K = 0.999;
    var POW_RATE = 0.0001;
    var POW_RANGE = 0.8;

    function SPEED_X() { return 8 + RND() * 4; }
    function SPEED_Y() { return 6 + RND() * 2; }

    var arrBubs = [];
    var iBottom;
    var iRight;

    var SQRT = Math.sqrt;
    var ATAN2 = Math.atan2;
    var SIN = Math.sin;
    var COS = Math.cos;
    var ABS = Math.abs;
    var RND = Math.random;
    var ROUND = Math.round;

    function Timer(call, time) {
        var last = +new Date;
        var delay = 0;
        return setInterval(function() {
            var cur = +new Date;
            delay += (cur - last);
            last = cur;
            if (delay >= time) {
                call();
                delay %= time;
            }
        }, 1);
    }

    Timer(update, 17);

    function CreateBubble(regionIndex) {
        var regions = document.querySelectorAll(".region");
        var region = regions[regionIndex];
        var regionRect = region.getBoundingClientRect();

        var bub = new Bubble(10, bubbleIdCounter++);
        var startX = regionRect.left + Math.random() * (regionRect.width - 10);
        var startY = regionRect.top + Math.random() * (regionRect.height - 10);

        bub.setX(startX);
        bub.setY(startY);
        bub.vx = SPEED_X() * (Math.random() > 0.5 ? 1 : -1);
        bub.vy = SPEED_Y() * (Math.random() > 0.5 ? 1 : -1);
        bub.regionBounds = regionRect;

        arrBubs.push(bub);
        bub.oBox.onclick = function() { showPopup(bub.id); };
    }

    window.CreateBubble = CreateBubble;
    
    function update() {
        var n = arrBubs.length;
        var bub, bub2;

        updateWall();

        for (var i = 0; i < n; i++) {
            bub = arrBubs[i];
            bub.paint();
            bub.vx *= K;
            bub.vy *= K;

            if (RND() < 0.01) {
                var newTargetD = 10 + RND() * 80;
                bub.setTargetSize(newTargetD);
            }

            if (bub.D !== bub.targetD) {
                var delta = bub.targetD - bub.D;
                if (Math.abs(delta) < bub.sizeSpeed) {
                    bub.D = bub.targetD;
                } else {
                    bub.D += delta > 0 ? bub.sizeSpeed : -bub.sizeSpeed;
                }
                bub.updateSize();
            }

            if (RND() < POW_RATE) {
                bub.vx = SPEED_X() * (1 + RND() * POW_RANGE);
                bub.vy = SPEED_Y() * (1 + RND() * POW_RANGE);
            }

            bub.setX(bub.x + bub.vx);
            bub.setY(bub.y + bub.vy);
            checkWalls(bub);
        }

        for (var i = 0; i < n - 1; i++) {
            bub = arrBubs[i];
            for (var j = i + 1; j < n; j++) {
                bub2 = arrBubs[j];
                checkCollision(bub, bub2);
            }
        }
    }

    function updateWall() {
        iRight = de.clientWidth;
        iBottom = de.clientHeight;
    }

    function checkWalls(bub) {
        var bounds = bub.regionBounds;

        if (bub.x < bounds.left) {
            bub.setX(bounds.left);
            bub.vx *= -1;
        } else if (bub.x > bounds.right - bub.D) {
            bub.setX(bounds.right - bub.D);
            bub.vx *= -1;
        }

        if (bub.y < bounds.top) {
            bub.setY(bounds.top);
            bub.vy *= -1;
        } else if (bub.y > bounds.bottom - bub.D) {
            bub.setY(bounds.bottom - bub.D);
            bub.vy *= -1;
        }
    }

    function rotate(x, y, sin, cos, reverse) {
        if (reverse)
            return { x: x * cos + y * sin, y: y * cos - x * sin };
        else
            return { x: x * cos - y * sin, y: y * cos + x * sin };
    }

    function checkCollision(bub0, bub1) {
        var dx = bub1.x - bub0.x;
        var dy = bub1.y - bub0.y;
        var dist = SQRT(dx * dx + dy * dy);
        var minDist = (bub0.D + bub1.D) / 2;

        if (dist < minDist) {
            var angle = ATAN2(dy, dx);
            var sin = SIN(angle);
            var cos = COS(angle);

            var pos0 = { x: 0, y: 0 };
            var pos1 = rotate(dx, dy, sin, cos, true);

            var vel0 = rotate(bub0.vx, bub0.vy, sin, cos, true);
            var vel1 = rotate(bub1.vx, bub1.vy, sin, cos, true);

            var vxTotal = vel0.x - vel1.x;
            vel0.x = vel1.x;
            vel1.x = vxTotal + vel0.x;

            var absV = ABS(vel0.x) + ABS(vel1.x);
            var overlap = minDist - ABS(pos0.x - pos1.x);

            pos0.x += vel0.x / absV * overlap;
            pos1.x += vel1.x / absV * overlap;

            var pos0F = rotate(pos0.x, pos0.y, sin, cos, false);
            var pos1F = rotate(pos1.x, pos1.y, sin, cos, false);

            bub1.setX(bub0.x + pos1F.x);
            bub1.setY(bub0.y + pos1F.y);
            bub0.setX(bub0.x + pos0F.x);
            bub0.setY(bub0.y + pos0F.y);

            var vel0F = rotate(vel0.x, vel0.y, sin, cos, false);
            var vel1F = rotate(vel1.x, vel1.y, sin, cos, false);

            bub0.vx = vel0F.x;
            bub0.vy = vel0F.y;
            bub1.vx = vel1F.x;
            bub1.vy = vel1F.y;
        }
    }

    var APLHA = 0.8;
    var POW = [1, APLHA, APLHA * APLHA];

    function Bubble(initialD, id) {
        this.D = initialD;
        this.targetD = initialD;
        this.sizeSpeed = 2;
        this.id = id;
        var kOpa = [], kStp = [];
        var arrFlt = [];
        var oBox = document.body.appendChild(document.createElement("div"));

        styBox = oBox.style;
        styBox.position = "absolute";
        styBox.width = this.D + "px";
        styBox.height = this.D + "px";

        for (var i = 0; i < 4; i++) {
            var div = document.createElement("div");
            var sty = div.style;
            sty.position = "absolute";
            sty.width = this.D + "px";
            sty.height = this.D + "px";
            oBox.appendChild(div);
            if (i == 3) {
                if (_IE6_)
                    sty.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(src=heart.png)";
                else
                    sty.backgroundImage = "url(heart.png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                break;
            }
            kOpa[i] = 3 * RND();
            kStp[i] = 0.02 * RND();
            if (STD) {
                sty.backgroundImage = "url(ch" + i + ".png)";
                sty.backgroundSize = this.D + "px " + this.D + "px";
                arrFlt[i] = sty;
            } else {
                sty.filter = "alpha progid:DXImageTransform.Microsoft.AlphaImageLoader(src=ch" + i + ".png)";
                arrFlt[i] = div.filters.alpha;
            }
        }

        this.styBox = styBox;
        this.oBox = oBox;
        this.kOpa = kOpa;
        this.kStp = kStp;
        this.arrFlt = arrFlt;
    }

    Bubble.prototype.setX = function(x) {
        this.x = x;
        this.styBox.left = ROUND(x) + "px";
    };

    Bubble.prototype.setY = function(y) {
        this.y = y;
        this.styBox.top = ROUND(y) + "px";
    };

    Bubble.prototype.setTargetSize = function(newTargetD) {
        this.targetD = newTargetD;
    };

    Bubble.prototype.updateSize = function() {
        this.styBox.width = this.D + "px";
        this.styBox.height = this.D + "px";
        for (var i = 0; i < this.oBox.children.length; i++) {
            var div = this.oBox.children[i];
            div.style.width = this.D + "px";
            div.style.height = this.D + "px";
            div.style.backgroundSize = this.D + "px " + this.D + "px";
        }
    };

    Bubble.prototype.paint = function() {
        var i, v;
        for (i = 0; i < 3; i++) {
            v = ABS(SIN(this.kOpa[i] += this.kStp[i] * RND()));
            v *= POW[i];
            v = ((v * 1e4) >> 0) / 1e4;
            this.arrFlt[i].opacity = STD ? v : v * 100;
        }
    };

    function showPopup(bubbleId) {
        document.getElementById('bubbleId').innerText = "泡泡 ID: " + bubbleId;
        document.getElementById('bubbleImage').src = "static/science/" + bubbleId + ".jpg";
        document.getElementById('bubblePopup').style.display = 'block';
    }

    function closePopup() {
        document.getElementById('bubblePopup').style.display = 'none';
    }

    document.getElementById('closeButton').onclick = closePopup;

}();
</script>
</body>
</html>
