USE [edsynwx]
GO

/****** Object:  Table [dbo].[StoreTeachingAssistants]    Script Date: 2025/5/13 14:06:34 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[StoreTeachingAssistants](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[StoreID] [int] NOT NULL,
	[AssistantName] [nvarchar](50) NOT NULL,
	[ContactNumber] [varchar](20) NOT NULL,
	[Email] [varchar](100) NULL,
	[WorkingDays] [nvarchar](100) NULL,
	[WorkingHours] [nvarchar](100) NULL,
	[Status] [tinyint] NOT NULL,
	[CreateTime] [datetime] NOT NULL,
	[UpdateTime] [datetime] NOT NULL,
	[AvatarURL] [nvarchar](255) NULL,
	[WechatQRCodeURL] [nvarchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[StoreTeachingAssistants] ADD  DEFAULT ((1)) FOR [Status]
GO

ALTER TABLE [dbo].[StoreTeachingAssistants] ADD  DEFAULT (getdate()) FOR [CreateTime]
GO

ALTER TABLE [dbo].[StoreTeachingAssistants] ADD  DEFAULT (getdate()) FOR [UpdateTime]
GO

ALTER TABLE [dbo].[StoreTeachingAssistants]  WITH CHECK ADD  CONSTRAINT [FK_StoreTeachingAssistants_Stores] FOREIGN KEY([StoreID])
REFERENCES [dbo].[Stores] ([ID])
GO

ALTER TABLE [dbo].[StoreTeachingAssistants] CHECK CONSTRAINT [FK_StoreTeachingAssistants_Stores]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'助教ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'StoreTeachingAssistants', @level2type=N'COLUMN',@level2name=N'ID'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'门店ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'StoreTeachingAssistants', @level2type=N'COLUMN',@level2name=N'StoreID'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'助教姓名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'StoreTeachingAssistants', @level2type=N'COLUMN',@level2name=N'AssistantName'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'联系电话' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'StoreTeachingAssistants', @level2type=N'COLUMN',@level2name=N'ContactNumber'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'电子邮箱' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'StoreTeachingAssistants', @level2type=N'COLUMN',@level2name=N'Email'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'StoreTeachingAssistants', @level2type=N'COLUMN',@level2name=N'WorkingDays'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'StoreTeachingAssistants', @level2type=N'COLUMN',@level2name=N'WorkingHours'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态(1:在职 0:离职)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'StoreTeachingAssistants', @level2type=N'COLUMN',@level2name=N'Status'
GO


