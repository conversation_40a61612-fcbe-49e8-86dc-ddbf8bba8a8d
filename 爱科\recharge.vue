<template>
    <view>
      <u-swiper 
        :list="swiperlist1" 
        keyName="image" 
        showTitle 
        :height="350" 
        indicator-active-color="#007AFF" 
        indicator-inactive-color="#CCC" 
        autoplay 
        circular
        @click="goToPage">  
      </u-swiper>
  
      <!-- 会员充值页面表单 -->
      <view class="form-container">
        <form @submit.prevent="submitForm">
          <!-- 选择使用人 -->
          <view class="form-item">
            <text class="form-label">选择使用人：</text>
            <view class="checkbox-group">
              <checkbox-group v-model="selectedUsers" @change="updateSelectedUsers">
                <view v-for="(user, index) in availableUsers" :key="index" class="checkbox-item">
                  <checkbox :value="user.cardNumber">
                    {{ user.name }} ({{ user.cardNumber }} - {{ user.cardType }})
                  </checkbox>
                </view>
              </checkbox-group>
            </view>
          </view>
  
          <!-- 显示已选使用人 -->
          <view v-if="formData.users && formData.users.length > 0" class="user-list">
            <view v-for="(user, index) in formData.users" :key="index" class="user-row">
              <view class="user-info">
                <text>姓名: {{ user.name }}</text>
                <text>卡号: {{ user.cardNumber }}</text>
                <text>卡片类型: {{ user.cardType }}</text>
                <input 
                  type="number" 
                  v-model="user.rechargeAmount" 
                  placeholder="请输入充值金额"
                  class="recharge-input"
                />
              <!-- 选择开始日期 -->
              <picker mode="date" @change="handleDateChange(index, 'startDate', $event)">
                <view class="pickergrade">
                  {{ user.startDate || '请选择开始日期' }}
                </view>
              </picker>
                            
              <!-- 选择结束日期 -->
              <picker mode="date" @change="handleDateChange(index, 'endDate', $event)">
                <view class="pickergrade">
                  {{ user.endDate || '请选择结束日期' }}
                </view>
              </picker>
              </view>
            </view>
          </view>
  
          <!-- 缴费方式 -->
          <view class="form-item">
            <text class="form-label">缴费方式：</text>
            <picker mode="selector" :range="paymentOptions" @change="handlePaymentMethodChange">
              <view class="pickergender">
                {{ formData.paymentMethod || '请选择缴费方式' }}
              </view>
            </picker>
          </view>
  
          <view class="button-container">
            <button class="btn" @click="goBack">返回</button>
            <button class="btn btn-submit" type="submit">提交</button>
          </view>
        </form>
      </view>
    </view>
  </template>
  
  <script>
  import { generateSignature } from '@/utils/auth'

  export default {
    data() {
      return {
        swiperlist1: [
          { image: "/static/swiper/swiper0.jpg", title: "昨夜星辰昨夜风，画楼西畔桂堂东", url: "/pages/index/index" },
          { image: "/static/swiper/swiper1.jpg", title: "身无彩凤双飞翼，心有灵犀一点通", url: "/pages/index/index" },
          { image: "/static/swiper/swiper2.jpg", title: "谁念西风独自凉，萧萧黄叶闭疏窗，沉思往事立残阳", url: "/pages/index/index" }
        ],
        formData: {
          users: [], // 保存已选用户信息
          rechargeAmount: "",
          usagePeriod: [], // 初始化为数组
          paymentMethod: "",
        },
        paymentOptions: ["微信支付", "云闪付"],
        availableUsers: [],
        selectedUsers: [], // 绑定选中的用户卡号
      };
    },
    onLoad() {
      this.fetchChildrenInfo() // 页面加载时调用获取孩子信息
    },
    methods: {
      goBack() {
        uni.navigateBack();
      },
      async fetchChildrenInfo() {
        try {
          const openid = uni.getStorageSync('openid')
          if (!openid) {
            uni.showToast({ title: '请先登录', icon: 'none' })
            return
          }
  
          const timestamp = Math.floor(Date.now() / 1000)
          const nonce = Math.random().toString(36).substring(2, 10)
          const signature = generateSignature(timestamp, nonce)
  
          const res = await uni.request({
            url: 'https://aikexiaozhan.com:9090/api/getChildrenInfo',
            method: 'POST',
            data: { openid },
            header: {
              'X-Timestamp': timestamp,
              'X-Nonce': nonce,
              'X-Signature': signature,
              'Content-Type': 'application/json'
            }
          })
  
          if (res.statusCode === 200) {
            this.availableUsers = res.data.children.map(child => ({
              name: child.name,
              cardNumber: child.cardNumber || '',
              cardType: child.cardType || '普通卡',
              startDate: '',
              endDate: '',
              rechargeAmount: ''
            }))
            
            // 检查是否是首次充值(没有卡号)
            this.isFirstRecharge = res.data.children.some(child => !child.cardNumber)
          }
        } catch (error) {
          console.error('获取孩子信息失败:', error)
          uni.showToast({ title: '获取信息失败', icon: 'none' })
        }
      },
      async submitForm() {
        // 表单验证
        if (!this.validateForm()) {
            return
        }
        
        try {
            const timestamp = Math.floor(Date.now() / 1000)
            const nonce = Math.random().toString(36).substring(2, 10)
            const signature = generateSignature(timestamp, nonce)
            
            // 1. 创建订单
            const orderRes = await uni.request({
                url: 'https://aikexiaozhan.com:9090/api/createOrder',
                method: 'POST',
                data: {
                    ...this.formData,
                    openid: uni.getStorageSync('openid'),
                    isFirstRecharge: this.isFirstRecharge
                },
                header: {
                    'X-Timestamp': timestamp,
                    'X-Nonce': nonce,
                    'X-Signature': signature,
                    'Content-Type': 'application/json'
                }
            })
            
            if (orderRes.statusCode !== 200) {
                uni.showToast({ title: '创建订单失败', icon: 'none' })
                return
            }
            
            const orderData = orderRes.data.data
            
            // 2. 调用支付
            if (this.formData.paymentMethod === '微信支付') {
                const paymentRes = await uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: orderData.paymentParams.timeStamp,
                    nonceStr: orderData.paymentParams.nonceStr,
                    package: orderData.paymentParams.package,
                    signType: orderData.paymentParams.signType,
                    paySign: orderData.paymentParams.paySign
                })
                
                if (paymentRes.errMsg === 'requestPayment:ok') {
                    await this.handlePaymentSuccess(orderData.orderId)
                } else {
                    uni.showToast({ title: '支付失败: ' + paymentRes.errMsg, icon: 'none' })
                }
                
            } else if (this.formData.paymentMethod === '云闪付') {
                // 调用云闪付支付
                const paymentRes = await uni.requestPayment({
                    provider: 'unionpay',
                    tn: orderData.paymentParams.tn
                })
                
                if (paymentRes.errMsg === 'requestPayment:ok') {
                    await this.handlePaymentSuccess(orderData.orderId)
                } else {
                    uni.showToast({ title: '支付失败: ' + paymentRes.errMsg, icon: 'none' })
                }
            }
            
        } catch (error) {
            console.error('支付过程出错:', error)
            uni.showToast({ title: '支付过程出错', icon: 'none' })
        }
      },
      handlePaymentMethodChange(event) {
        const selectedMethod = this.paymentOptions[event.detail.value];
        this.formData.paymentMethod = selectedMethod;
      },
      goToPage(page) {
        uni.navigateTo({
          url: this.swiperlist1[page].url,
        });
      },
      // 更新选中的用户信息
      updateSelectedUsers(e) {
        // 获取选中的用户卡号
        const selectedCardNumbers = e.detail.value;  // 这是一个数组，包含选中的卡号
        // 根据选中的卡号更新 formData.users
        this.formData.users = this.availableUsers.filter(user => selectedCardNumbers.includes(user.cardNumber));
        //console.log(this.formData.users.length); // 输出调试信息
      },
      // 处理日期选择的事件
      handleDateChange(userIndex, field, event) {
        const selectedDate = event.detail.value;
        // 使用正则表达式校验日期格式
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (dateRegex.test(selectedDate)) {
          // 根据索引找到对应的用户对象并更新日期
          this.formData.users[userIndex][field] = selectedDate;
          console.log(selectedDate);
        } else {
          // 日期格式不正确，提供默认值或提示用户
          console.error('日期格式不正确，已设置为默认值');
          this.formData.users[userIndex][field] = ''; // 设置为空或默认日期
          uni.showToast({
            title: '日期格式不正确，请重新选择',
            icon: 'none'
          });
        }
      }
    },
  };
  </script>
  
  <style scoped>
  .form-container {
    margin: 0 auto;
    padding: 20px;
  }
  
  .form-item {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
  }
  
  .form-label {
    margin-bottom: 5px;
    font-size: 16px;
    color: #333;
  }
  
  input {
    width: 90%;
    height: 40px;
    padding: 5px 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  
  .recharge-input {
    margin-top: 10px;
    width: 90%;
    height: 40px;
    padding: 5px 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  
  .pickergender,
  .pickergrade {
    width: 90%;
    height: 40px;
    line-height: 40px;
    text-align: left;
    padding-left: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
  }
  
  .button-container {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }
  
  .btn {
    width: 48%;
    height: 40px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #007AFF;
    color: white;
    border: none;
    border-radius: 5px;
  }
  
  .btn-submit {
    background-color: #28a745;
  }
  
  .user-list {
    margin: 20px 0;
  }
  
  .user-row {
    display: flex;
    flex-direction: column;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 10px;
  }
  
  .user-info text {
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
  }
  
  button {
    background-color: #FF4C4C;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 5px 10px;
  }
  </style>
