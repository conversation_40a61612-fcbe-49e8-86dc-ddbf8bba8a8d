#property copyright "Copyright 2023, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

input double Lots = 0.01;
input int ConsecutiveBars = 9;
input int AddPips = 50;
input double Multiplier = 1.3;
input int TakeProfitPips = 100;
input int MagicNumber = 12345;

int consecutiveBullish = 0;
int consecutiveBearish = 0;
datetime lastBarTime = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ObjectsDeleteAll(0, "ConsecutiveMark_");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   if(CheckNewBar())
   {
      CheckConsecutiveBars();
      OpenInitialOrders();
      AddToPosition();
      CheckCloseAll();
      MarkBars();
   }
}

//+------------------------------------------------------------------+
//| 检查新K线                                                         |
//+------------------------------------------------------------------+
bool CheckNewBar()
{
   datetime currentBarTime = iTime(Symbol(), Period(), 0);
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 检查连续K线                                                       |
//+------------------------------------------------------------------+
void CheckConsecutiveBars()
{
   double prevOpen = iOpen(Symbol(), Period(), 1);
   double prevClose = iClose(Symbol(), Period(), 1);

   if(prevClose > prevOpen)
   {
      consecutiveBullish++;
      consecutiveBearish = 0;
   }
   else if(prevClose < prevOpen)
   {
      consecutiveBearish++;
      consecutiveBullish = 0;
   }
   else
   {
      consecutiveBullish = 0;
      consecutiveBearish = 0;
   }
}

//+------------------------------------------------------------------+
//| 开首单逻辑                                                        |
//+------------------------------------------------------------------+
void OpenInitialOrders()
{
   if(consecutiveBullish == ConsecutiveBars)
   {
      OpenOrder(OP_SELL, Lots);
   }
   else if(consecutiveBearish == ConsecutiveBars)
   {
      OpenOrder(OP_BUY, Lots);
   }
}

//+------------------------------------------------------------------+
//| 下单函数                                                          |
//+------------------------------------------------------------------+
void OpenOrder(int cmd, double lot)
{
   double price = (cmd == OP_BUY) ? Ask : Bid;
   int ticket = OrderSend(Symbol(), cmd, lot, price, 3, 0, 0, "EA", MagicNumber, 0, clrNONE);
   
   if(ticket < 0)
      Print("OrderSend failed with error ", GetLastError());
}

//+------------------------------------------------------------------+
//| 加仓逻辑                                                          |
//+------------------------------------------------------------------+
void AddToPosition()
{
   int lastTicket = -1;
   double lastLot = 0;
   int lastType = -1;
   datetime lastTime = 0;

   for(int i = OrdersTotal()-1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            if(OrderOpenTime() > lastTime)
            {
               lastTicket = OrderTicket();
               lastLot = OrderLots();
               lastType = OrderType();
               lastTime = OrderOpenTime();
            }
         }
      }
   }

   if(lastTicket == -1) return;

   double currentPrice = (lastType == OP_BUY) ? Bid : Ask;
   double priceDiff = (lastType == OP_BUY) ? (OrderOpenPrice() - currentPrice) : (currentPrice - OrderOpenPrice());
   double pipDiff = priceDiff / (Point * 10);

   if(pipDiff >= AddPips)
   {
      double newLot = NormalizeDouble(lastLot * Multiplier, 2);
      OpenOrder(lastType, newLot);
   }
}

//+------------------------------------------------------------------+
//| 检查平仓条件                                                      |
//+------------------------------------------------------------------+
void CheckCloseAll()
{
   double totalPips = 0;

   for(int i = OrdersTotal()-1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            double currentPrice = (OrderType() == OP_BUY) ? Bid : Ask;
            double priceDiff = currentPrice - OrderOpenPrice();
            if(OrderType() == OP_SELL) priceDiff = -priceDiff;
            
            totalPips += (priceDiff / (Point * 10));
         }
      }
   }

   if(totalPips >= TakeProfitPips)
      CloseAllOrders();
}

//+------------------------------------------------------------------+
//| 平仓所有订单                                                      |
//+------------------------------------------------------------------+
void CloseAllOrders()
{
   for(int i = OrdersTotal()-1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            double price = (OrderType() == OP_BUY) ? Bid : Ask;
            bool res = OrderClose(OrderTicket(), OrderLots(), price, 3, clrNONE);
            if(!res)
               Print("OrderClose failed ", GetLastError());
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 在K线上标记                                                       |
//+------------------------------------------------------------------+
void MarkBars()
{
   datetime barTime = iTime(Symbol(), Period(), 1);
   double high = iHigh(Symbol(), Period(), 1);
   
   string objName = "ConsecutiveMark_" + IntegerToString(barTime);
   ObjectDelete(0, objName);
   
   string text = "";
   if(consecutiveBullish > 0)
      text = "B" + IntegerToString(consecutiveBullish);
   else if(consecutiveBearish > 0)
      text = "S" + IntegerToString(consecutiveBearish);
   else
      text = "0";
   
   ObjectCreate(0, objName, OBJ_TEXT, 0, barTime, high);
   ObjectSetText(objName, text, 8, "Arial", clrWhite);
   ObjectSetInteger(0, objName, OBJPROP_ANCHOR, ANCHOR_TOP_RIGHT);
}