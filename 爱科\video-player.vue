<template>
    <view class="player-container">
      <polyv-player 
        id="polyvPlayer"
        :playerId="playerId"
        :width="'100%'"
        :height="'500rpx'"
        :vid="currentVideo.vid"
        :viewerInfo="viewerInfo"
        :autoplay="true"
        :showControls="true"
        :showFullscreenBtn="true"
        :showProgressBar="true"
        :enableAutoRotation="true"
        @statechange="onStateChange"
      >
      </polyv-player>
      
      <view class="video-info">
        <text class="video-title">{{ currentVideo.title }}</text>
        <text class="video-category">{{ currentVideo.cateName }}</text>
      </view>

      <!-- 评论区 -->
      <view class="comment-section">
        <view class="comment-header">
          <text>评论区</text>
        </view>
        
        <view class="comment-list">
          <view v-for="comment in comments" :key="comment.id" class="comment-item">
            <view class="comment-user">{{ comment.user_name }}</view>
            <view class="comment-content">{{ comment.content }}</view>
            <view class="comment-time">{{ comment.created_at }}</view>
            <button class="reply-btn" @click="showReplyInput(comment.id)">回复</button>
            
            <!-- 回复输入框 -->
            <view v-if="activeReplyId === comment.id" class="reply-input">
              <input v-model="replyContent" placeholder="写下你的回复..." />
              <button @click="submitReply(comment.id)">提交</button>
            </view>
            
            <!-- 子评论 -->
            <view v-if="comment.replies && comment.replies.length" class="replies">
              <view v-for="reply in comment.replies" :key="reply.id" class="reply-item">
                <view class="comment-user">{{ reply.user_name }}</view>
                <view class="comment-content">{{ reply.content }}</view>
                <view class="comment-time">{{ reply.created_at }}</view>
              </view>
            </view>
          </view>
        </view>

        <view class="comment-input">
          <input v-model="newComment" placeholder="写下你的评论..." />
          <button @click="submitComment">提交</button>
        </view>
      </view>
    </view>
</template>

<script>
import { generateSignature } from '@/utils/auth'
export default {
    data() {
      return {
        playerId: 'player-' + Date.now(),
        currentVideo: {},
        viewerInfo: {
          userId: '',
          userName: ''
        },
        comments: [],
        newComment: '',
        activeReplyId: null,
        replyContent: '',
        replyingTo: null
      };
    },
    onLoad(options) {
      if (options.video) {
        this.currentVideo = JSON.parse(options.video);
        this.loadComments();
      }
    },
    methods: {
      onStateChange(event) {
        console.log('播放器状态变化:', event.detail);
      },
      async loadComments() {
        try {
		  const timestamp = Math.floor(Date.now() / 1000);
		  const nonce = Math.random().toString(36).substring(2, 10);
		  const signature = generateSignature(timestamp, nonce);
          const res = await uni.request({
            url: `https://aikexiaozhan.com/api/video-comments/${this.currentVideo.vid}`,
            method: 'GET',
			header: {
			  'X-Timestamp': timestamp,
			  'X-Nonce': nonce,
			  'X-Signature': signature,
			  'Content-Type': 'application/json'
			},
          });
          if (res.statusCode === 200) {
            this.comments = res.data;
			console.log(res.data);
          }
        } catch (error) {
          console.error('加载评论失败:', error);
        }
      },
      async submitComment() {
        if (!this.newComment.trim()) return;
        
        try {
          const timestamp = Math.floor(Date.now() / 1000);
          const nonce = Math.random().toString(36).substring(2, 10);
          const signature = generateSignature(timestamp, nonce);
		  const openid = uni.getStorageSync('openid');
          const res = await uni.request({
            url: 'https://aikexiaozhan.com/api/submit-comment',
            method: 'POST',
			header: {
			  'X-Timestamp': timestamp,
			  'X-Nonce': nonce,
			  'X-Signature': signature,
			  'Content-Type': 'application/json'
			},
            data: {
              vid: this.currentVideo.vid,
              content: this.newComment,
              openid: openid
            }
          });
          
          if (res.statusCode === 200) {
            this.newComment = '';
            this.loadComments();
            uni.showToast({ title: '评论成功', icon: 'success' });
          }
        } catch (error) {
          console.error('提交评论失败:', error);
          uni.showToast({ title: '评论失败', icon: 'none' });
        }
      },
    
      showReplyInput(commentId) {
        this.activeReplyId = commentId;
        this.replyContent = '';
      },
      
      async submitReply(parentCommentId) {
        if (!this.replyContent.trim()) return;
        
        try {
          const timestamp = Math.floor(Date.now() / 1000);
          const nonce = Math.random().toString(36).substring(2, 10);
          const signature = generateSignature(timestamp, nonce);
          const openid = uni.getStorageSync('openid');
          
          const res = await uni.request({
            url: 'https://aikexiaozhan.com/api/submit-comment',
            method: 'POST',
            header: {
              'X-Timestamp': timestamp,
              'X-Nonce': nonce,
              'X-Signature': signature,
              'Content-Type': 'application/json'
            },
            data: {
              vid: this.currentVideo.vid,
              content: this.replyContent,
              openid: openid,
              parent_comment_id: parentCommentId
            }
          });
          
          if (res.statusCode === 200) {
            this.replyContent = '';
            this.activeReplyId = null;
            this.loadComments();
            uni.showToast({ title: '回复成功', icon: 'success' });
          }
        } catch (error) {
          console.error('提交回复失败:', error);
          uni.showToast({ title: '回复失败', icon: 'none' });
        }
      }
    }
};
</script>

<style scoped>
.player-container {
  padding: 20px;
}
.video-info {
  margin-top: 20px;
}
.video-title {
  font-size: 18px;
  font-weight: bold;
}
.video-category {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}

/* 评论区样式 */
.comment-section {
  margin-top: 30px;
  border-top: 1px solid #eee;
  padding-top: 20px;
}
.comment-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}
.comment-item {
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.reply-btn {
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 12px;
  padding: 2px 8px;
}

.replies {
  margin-left: 20px;
  padding-left: 10px;
  border-left: 2px solid #eee;
}

.reply-item {
  padding: 8px 0;
  border-bottom: 1px dashed #f0f0f0;
}

.reply-input {
  display: flex;
  margin-top: 10px;
}

.reply-input input {
  flex: 1;
  border: 1px solid #ddd;
  padding: 6px;
  border-radius: 4px;
  margin-right: 8px;
  font-size: 14px;
}
.comment-user {
  font-weight: bold;
  font-size: 14px;
}
.comment-content {
  font-size: 14px;
  margin: 5px 0;
}
.comment-time {
  font-size: 12px;
  color: #999;
}
.comment-input {
  display: flex;
  margin-top: 15px;
}
.comment-input input {
  flex: 1;
  border: 1px solid #ddd;
  padding: 8px;
  border-radius: 4px;
  margin-right: 10px;
}
</style>