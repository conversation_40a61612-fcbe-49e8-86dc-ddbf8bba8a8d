-- 检查 VideoList 表的所有索引信息
-- 在修改字段类型前运行此脚本了解现有索引

USE [edsynwx]
GO

PRINT '=== VideoList 表的所有索引信息 ==='

-- 查看所有索引
SELECT 
    i.name AS 索引名称,
    i.type_desc AS 索引类型,
    i.is_unique AS 是否唯一,
    i.is_primary_key AS 是否主键,
    c.name AS 字段名称,
    c.system_type_name AS 字段类型,
    ic.key_ordinal AS 索引中的位置
FROM sys.indexes i
INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
INNER JOIN sys.dm_exec_describe_first_result_set_for_object(OBJECT_ID('dbo.VideoList'), NULL) c 
    ON ic.column_id = c.column_ordinal
WHERE i.object_id = OBJECT_ID('dbo.VideoList')
ORDER BY i.name, ic.key_ordinal;

PRINT ''
PRINT '=== 涉及 varchar 字段的索引 ==='

-- 专门查看涉及 varchar 字段的索引
SELECT 
    i.name AS 索引名称,
    c.name AS 字段名称,
    t.name AS 当前数据类型,
    c.max_length AS 最大长度,
    i.type_desc AS 索引类型,
    i.is_unique AS 是否唯一
FROM sys.indexes i
INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
INNER JOIN sys.types t ON c.user_type_id = t.user_type_id
WHERE i.object_id = OBJECT_ID('dbo.VideoList')
    AND t.name = 'varchar'
    AND i.name IS NOT NULL
ORDER BY i.name;

PRINT ''
PRINT '=== 生成删除索引的脚本 ==='

-- 生成删除涉及 varchar 字段的索引的脚本
SELECT 
    'DROP INDEX [' + i.name + '] ON [dbo].[VideoList];' AS 删除索引脚本
FROM sys.indexes i
INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
INNER JOIN sys.types t ON c.user_type_id = t.user_type_id
WHERE i.object_id = OBJECT_ID('dbo.VideoList')
    AND t.name = 'varchar'
    AND i.name IS NOT NULL
    AND i.is_primary_key = 0  -- 排除主键
GROUP BY i.name;

GO
