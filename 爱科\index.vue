<template>
  <view>
	<!-- 全屏广告 -->
	<view v-if="showFullScreenAd" class="fullscreen-ad" @click="handleAdClick">
	  <image :src="fullScreenAd.imageUrl" mode="aspectFill" class="ad-image"></image>
	  <view class="ad-close" @click.stop="showFullScreenAd = false">
	    <text class="countdown">{{countdown}}s</text>
	    <uni-icons type="close" size="24" color="#fff"></uni-icons>
	  </view>
	</view>
    <view v-if="loading" class="loading-container">
      <uni-loading mode="circle" color="#007AFF"></uni-loading>
    </view>
	<view class="header-title">
      爱迪生科学小站
    </view>    
    <swiper 
	  class="swiper-container1"
	  :autoplay="true"
	  :circular="true"
	  :indicator-dots="true"
	  indicator-active-color="#007AFF"
	  indicator-color="#CCC"
	  :interval="3000"
	  :duration="500">
	  <swiper-item v-for="(item, index) in swiperlist1" :key="index">
		<view class="swiper-item" @click="goToPage('swiperlist1', index)">
		  <image :src="item.image" mode="aspectFill" class="swiper-image" />
		  <view class="swiper-title">{{item.title}}</view>
		</view>
	  </swiper-item>
	</swiper>
    <!-- 会员信息栏 -->
    <view class="member-info">
      <view v-if="isLoggedIn" style="font-size: 16px; color: #333; display: block; padding-left: 10px;">
		  <image src="/static/jiazhang.png" mode="widthFix" style="width: 35px; vertical-align: middle" /><span style="margin: 0 10px; color: #BBB">|</span>{{ username }}
	  </view>
      <button v-else @click="goToRegister">
        注册登录
      </button>
    </view>
    <!-- 水平排列的两个按钮 -->
    <view class="button-container">
      <button class="action-button" @click="goToSciencePage">
        <image src="/static/ScienceStation.png" mode="widthFix" style="width: 100%; height: auto" />
      </button>
      <button class="action-button" @click="goToLearningPage">
        <image src="/static/ParentClassroom.png" mode="widthFix" style="width: 100%; height: auto" />
      </button>
    </view>	
    <view class="static-image-container">
      <image src="/static/kaiye.png" mode="aspectFill" class="static-image" />
    </view>	
	<view class="footer">
	  <text>版本号v1.0.11 @上海爱科小站文化科技发展有限公司</text>
	  <text>备案号：沪ICP备2025120811号-2X</text>
	</view>
  </view>
</template>

<script>
import { generateSignature } from '@/utils/auth'
export default {
  data() {
    return {
      loading: false,  // 新增加载状态
	  showFullScreenAd: false,
	  fullScreenAd: null,
	  adTimer: null,
	  countdown: 10,
      swiperlist1: [],
      isLoggedIn: false,
      username: "",
      openid: ""  // 新增openid字段
    };
  },
  methods: {
	 startCountdown() {
	     this.countdown = 10;
	     const timer = setInterval(() => {
	       this.countdown--;
	       if (this.countdown <= 0) {
	         clearInterval(timer);
	         this.showFullScreenAd = false;
	       }
	     }, 1000);
	     return timer;
	   }, 
	async fetchFullScreenAd() {
	    try {
			const timestamp = Math.floor(Date.now() / 1000);
			const nonce = Math.random().toString(36).substring(2, 10);
			const signature = generateSignature(timestamp, nonce);
	      const res = await uni.request({
	        url: 'https://aikexiaozhan.com/api/getLatestFullScreenAd',
	        method: 'POST',
			header: {
			  'X-Timestamp': timestamp,
			  'X-Nonce': nonce,
			  'X-Signature': signature,
			  'Content-Type': 'application/json'
			},
	        timeout: 5000
	      });
	      
	      if (res.statusCode === 200 && res.data.data) {
	              this.fullScreenAd = res.data.data;
	              this.showFullScreenAd = true;
	              this.adTimer = this.startCountdown();
	            }
	    } catch (error) {
	      console.error('获取全屏广告失败:', error);
	    }
	  },
	  handleAdClick() {
	    if (this.fullScreenAd?.link) {
	      // 记录广告点击
	      uni.request({
	        url: 'https://aikexiaozhan.com/api/recordAdClick',
	        method: 'POST',
	        data: { adId: this.fullScreenAd.adId }  
	      });
	      
	      if (this.fullScreenAd.link.startsWith('http')) {
	        uni.navigateTo({
	          url: `/pages/webview/webview?url=${encodeURIComponent(this.fullScreenAd.link)}`
	        });
	      } else {
	        uni.navigateTo({
	          url: this.fullScreenAd.link
	        });
	      }
	    }
	    this.showFullScreenAd = false;
	    clearTimeout(this.adTimer);
	  },
    // 获取 swiper 数据
    async fetchSwiperData(category, listName) {
	  // 先从本地存储获取数据
		const storageKey = `swiper_${category}`;
		const cachedData = uni.getStorageSync(storageKey);
		
		// 如果有缓存数据且未过期，直接使用
		if (cachedData) {
		  const { data, timestamp } = cachedData;
		  // 设置缓存有效期为1小时
		  if (Date.now() - timestamp < 3600000) {
			this[listName] = data;
			return;
		  }
		}
      this.loading = true;
      try {
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = Math.random().toString(36).substring(2, 10);
        const signature = generateSignature(timestamp, nonce);
        
        const res = await uni.request({
          url: `https://aikexiaozhan.com/api/swiper/${category}`,
          method: "POST",
          header: {
            'X-Timestamp': timestamp,
            'X-Nonce': nonce,
            'X-Signature': signature,
            'Content-Type': 'application/json'
          },
          timeout: 5000  // 添加超时设置
        });
        
        if (res.statusCode === 200) {
          this[listName] = res.data;
		  // 将数据存入本地存储
			uni.setStorageSync(storageKey, {
			  data: res.data,
			  timestamp: Date.now()
			});
        } else {
          uni.showToast({
            title: `获取${category}数据失败`,
            icon: 'none'
          });
        }
      } catch (error) {
        console.error(`请求${category}失败`, error);
        uni.showToast({
          title: '网络请求失败，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },  
      // 跳转到对应页面
      goToPage(listType, pageIndex) {
        const currentList = this[listType];
        const currentItem = currentList[pageIndex];
        if (!currentItem?.adId) {
            uni.showToast({ title: '无效的页面链接', icon: 'none' });
            return;
        }
        
        // 添加防抖
        if (this.lastClickTime && Date.now() - this.lastClickTime < 1000) return;
        this.lastClickTime = Date.now();

        // 统一跳转到广告详情页，携带adId参数
        uni.navigateTo({
            url: `/pages/AdvertisingDetails/AdvertisingDetails?adId=${currentItem.adId}`
        });
      },
    // 跳转到注册页面
        goToRegister() {
          uni.navigateTo({
            url: '/pages/register/register' // 注册页面路径
          });
        },	 
    // 跳转到科学小站页面
    goToSciencePage() {
      uni.switchTab({
        url: '/pages/review/review' // 科学小站页面路径
      });
    },
    //家长课堂
    goToLearningPage() {
      uni.switchTab({
        url: '/pages/parent/parent' // T型学习法页面路径
      });
    },			
// 检查用户登录状态
    checkLoginStatus() {
	  //console.log('checkLoginStatus');
      const userInfo = uni.getStorageSync('userInfo'); 
      if (userInfo) {
        this.isLoggedIn = userInfo.isLoggedIn;
		this.username = userInfo.parentName;
		this.openid = uni.getStorageSync('openid');
      } else {
        this.isLoggedIn = false;
      }
    },
    // 处理tabbar点击切换
    onTabChange(value) {
      //console.log("Tab change triggered, selected value: ", value);
      const tabUrls = [
        '/pages/index/index',      // 首页
        '/pages/review/review',    // 复习
        '/pages/parent/parent',    // 家长
        '/pages/contact/contact',  // 联系
        '/pages/member/member'     // 会员
      ];
      uni.navigateTo({
        url: tabUrls[value]  // 根据点击的tab跳转到对应页面
      });
    },
	onLoad()  {
		this.checkLoginStatus();
		this.fetchFullScreenAd();
		this.fetchSwiperData("swiperlist1", "swiperlist1");
	   }
    }
};
</script>
<style scoped>
.fullscreen-ad {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999; /* 增加z-index确保在最上层 */
  background-color: rgba(0,0,0,0.95); /* 加深背景色 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.ad-image {
  width: 100%;
  height: 100%;
}

.ad-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}	
.header-title {
  width: 100%;
  height: 132px; /* 调整为与小程序胶囊按钮相同高度 */
  line-height: 132px; /* 确保文字垂直居中 */
  font-family: PingFang-SC, PingFang-SC;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  background-color: #F7664A;
  padding: 0 10px; /* 只保留左右padding */
  margin: 0 auto;
  box-sizing: border-box; /* 确保padding不影响总高度 */
}
.swiper-container1 {
  height: 200px;
  position: relative; /* 添加相对定位 */
  top: -30px; /* 上移30px与标题重叠 */
  margin-top: 0; /* 移除原有上边距 */
  width: calc(100% - 20px);
  margin: 0 10px;
}
.swiper-container2 {
  height: 200px;
  width: calc(100% - 20px); /* 左右各减去10px */
  margin: 0 10px; /* 左右各10px间距 */
}

.swiper-item {
  height: 100%;
  position: relative;
  width: 100%; /* 添加宽度100% */
}

.swiper-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 改为contain确保图片完整显示 */
  background-color: #f8f8f8; /* 添加背景色填充空白区域 */
}

.swiper-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  color: white;
  text-align: left;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 8px;
  font-size: 16px; /* 添加字体大小 */
  white-space: nowrap; /* 防止标题换行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}	
/* 会员信息栏样式 */
.member-info {
  padding: 5px;
  text-align: left; /* 修改为左对齐 */
  background-color: #fff;
}

.member-info text {
  font-size: 16px;
  color: #fff;
  display: block; /* 添加此属性确保独占一行 */
  padding-left: 5px; /* 添加左内边距 */
}


/* 水平排列的按钮容器 */
.button-container {
  display: flex;
  justify-content: space-around;
  padding: 10px;
  background-color: #fff;
}

/* 优化按钮样式 */
.action-button {
  width: 324rpx;
  height: 192rpx;
	border: none;
	background: none;
	padding: 0;
	margin: 0;
}

.action-button:active {
  transform: scale(0.98);
  opacity: 0.9; /* 点击时添加透明度变化 */
}

/* 添加响应式布局 */
@media (min-width: 768px) {
  .button-container {
    max-width: 600px;
    margin: 0 auto;
  }
}

.static-image-container {
  width: 100%;
  height: 100%;
  margin: 15px 0;
  display: flex;
  justify-content: center; /* 水平居中 */
}

.static-image {
  width: 360px;
  height: 220px;
}

.footer {
  padding: 20px 0;
  background-color: #f8f8f8;
  text-align: center;
  font-size: 12px;
  color: #999;
  line-height: 1.6;
  border-top: 1px solid #eee;
}

.footer text {
  display: block;
}
</style>