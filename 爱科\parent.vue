<template>
  <view>
    <view class="header-title">
      爱迪生科学小站
    </view>          
    <swiper
      class="swiper-container"
      :autoplay="true"
      :circular="true"
      :indicator-dots="true"
      indicator-active-color="#007AFF"
      indicator-color="#CCC"
      :interval="3000"
      :duration="500">
      <swiper-item v-for="(item, index) in swiperlist1" :key="index">
        <view class="swiper-item" @click="goToPage('swiperlist1', index)">
          <image :src="item.image" mode="aspectFill" class="swiper-image" />
          <view class="swiper-title">{{item.title}}</view>
        </view>
      </swiper-item>
    </swiper>
    <view class="content-placeholder">
      <text>正在更新中，敬请期待</text>
    </view>
  </view>
</template>

<script>
import { generateSignature } from '@/utils/auth'

export default {
  data() {
    return {
      swiperlist1: [],
      isLoggedIn: false,
      username: ""
    };
  },
  methods: {
    async fetchSwiperData(category, listName) {
      const storageKey = `swiper_${category}`;
      const cachedData = uni.getStorageSync(storageKey);
      
      if (cachedData) {
        const { data, timestamp } = cachedData;
        if (Date.now() - timestamp < 3600000) {
          this[listName] = data;
          return;
        }
      }
      
      try {
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = Math.random().toString(36).substring(2, 10);
        const signature = generateSignature(timestamp, nonce);
        
        const res = await uni.request({
          url: `https://aikexiaozhan.com/api/swiper/${category}`,
          method: "POST",
          header: {
            'X-Timestamp': timestamp,
            'X-Nonce': nonce,
            'X-Signature': signature,
            'Content-Type': 'application/json'
          },
          timeout: 5000
        });
        
        if (res.statusCode === 200) {
          this[listName] = res.data;
          uni.setStorageSync(storageKey, {
            data: res.data,
            timestamp: Date.now()
          });
        }
      } catch (error) {
        console.error(`请求${category}失败`, error);
      }
    },
    
    goToPage(listType, pageIndex) {
      const currentList = this[listType];
      const currentItem = currentList[pageIndex];
      if (!currentItem?.adId) return;
      
      uni.navigateTo({
        url: `/pages/AdvertisingDetails/AdvertisingDetails?adId=${currentItem.adId}`
      });
    },
    
    checkLoginStatus() {
      const userInfo = uni.getStorageSync('userInfo');
      this.isLoggedIn = !!userInfo;
      this.username = userInfo?.parentName || "";
    }
  },
  mounted() {
    this.checkLoginStatus();
    if (!this.isLoggedIn) {
      uni.redirectTo({ url: '/pages/register/register' });
      return;
    }
    this.fetchSwiperData("swiperlist1", "swiperlist1");
  }
};
</script>

<style scoped>
.header-title {
  width: 100%;
  height: 132px;
  line-height: 132px;
  font-family: PingFang-SC, PingFang-SC;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  text-align: left;
  background-color: #F7664A;
  padding: 0 10px;
  margin: 0 auto;
  box-sizing: border-box;
}

.swiper-container {
  height: 200px;
  position: relative;
  top: -30px;
  margin-top: 0;
  width: calc(100% - 20px);
  margin: 0 10px;
}

.swiper-item {
  height: 100%;
  position: relative;
  width: 100%;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.swiper-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  color: white;
  text-align: left;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10px;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content-placeholder {
  margin: 30px;
  padding: 20px;
  text-align: center;
  font-size: 16px;
  color: #666;
  background: #f8f8f8;
  border-radius: 8px;
}
</style>
